#!/usr/bin/env python3
"""
Test orientation detection and correction for shared_image_1.jpg
"""

import os
import cv2
from document_processor import DocumentProcessor

def test_orientation_detection():
    """Test orientation detection with shared_image_1.jpg"""
    
    processor = DocumentProcessor()
    
    test_image = 'uploads/shared_image_1.jpg'
    
    if not os.path.exists(test_image):
        print(f"❌ Test image not found: {test_image}")
        return
    
    print("🔄 Testing Orientation Detection and Correction")
    print("=" * 70)
    print(f"📄 Testing with: {os.path.basename(test_image)}")
    
    try:
        # Test original image first
        print(f"\n🔍 Testing original image extraction...")
        original_text = processor.extract_text_from_image(test_image)
        original_length = len(original_text.strip())
        original_quality = processor._calculate_text_quality(original_text)
        
        print(f"📊 Original: {original_length} characters, quality: {original_quality:.2f}")
        print(f"📄 Sample text: '{original_text[:100]}...'")
        
        # Test document identification
        doc_type = processor.identify_document_type(original_text)
        print(f"🏷️  Original identification: {doc_type}")
        
        # Test with orientation correction
        print(f"\n🔄 Testing with orientation correction...")
        
        # Read and preprocess with orientation correction
        img = cv2.imread(test_image)
        corrected_img = processor._detect_and_correct_orientation(img)
        
        # Save corrected image temporarily
        temp_corrected_path = 'temp_corrected.jpg'
        cv2.imwrite(temp_corrected_path, corrected_img)
        
        # Extract text from corrected image
        corrected_text = processor.extract_text_from_image(temp_corrected_path)
        corrected_length = len(corrected_text.strip())
        corrected_quality = processor._calculate_text_quality(corrected_text)
        
        print(f"📊 Corrected: {corrected_length} characters, quality: {corrected_quality:.2f}")
        print(f"📄 Sample text: '{corrected_text[:100]}...'")
        
        # Test document identification on corrected image
        corrected_doc_type = processor.identify_document_type(corrected_text)
        print(f"🏷️  Corrected identification: {corrected_doc_type}")
        
        # Compare results
        print(f"\n📈 Comparison:")
        print(f"   Text length: {original_length} -> {corrected_length} ({corrected_length - original_length:+d})")
        print(f"   Quality: {original_quality:.2f} -> {corrected_quality:.2f} ({corrected_quality - original_quality:+.2f})")
        print(f"   Document type: {doc_type} -> {corrected_doc_type}")
        
        if corrected_length > original_length * 1.5 or corrected_quality > original_quality + 0.1:
            print(f"✅ IMPROVEMENT: Orientation correction helped!")
        elif corrected_doc_type != 'Unknown' and doc_type == 'Unknown':
            print(f"✅ IMPROVEMENT: Document now correctly identified!")
        else:
            print(f"ℹ️  No significant improvement (original might be correctly oriented)")
        
        # Test structured data extraction
        if corrected_doc_type != 'Unknown':
            print(f"\n📊 Testing structured data extraction...")
            structured_data = processor.extract_structured_data(corrected_text, corrected_doc_type)
            
            if structured_data:
                print(f"✅ Extracted {len(structured_data)} fields:")
                for key, value in structured_data.items():
                    if key == 'Quality_Remarks':
                        print(f"   ⚠️  {key}: {value}")
                    else:
                        print(f"   📋 {key}: {value}")
            else:
                print(f"❌ No structured data extracted")
        
        # Clean up
        if os.path.exists(temp_corrected_path):
            os.remove(temp_corrected_path)
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def test_manual_rotations():
    """Test manual rotations to see which works best"""
    
    test_image = 'uploads/shared_image_1.jpg'
    
    if not os.path.exists(test_image):
        print(f"❌ Test image not found: {test_image}")
        return
    
    print(f"\n🔄 Testing Manual Rotations")
    print("=" * 70)
    
    processor = DocumentProcessor()
    
    # Read original image
    img = cv2.imread(test_image)
    
    rotations = [
        (0, "Original", img),
        (90, "90° CW", cv2.rotate(img, cv2.ROTATE_90_CLOCKWISE)),
        (180, "180°", cv2.rotate(img, cv2.ROTATE_180)),
        (270, "270° CW", cv2.rotate(img, cv2.ROTATE_90_COUNTERCLOCKWISE))
    ]
    
    results = []
    
    for angle, description, rotated_img in rotations:
        try:
            # Save rotated image temporarily
            temp_path = f'temp_rotation_{angle}.jpg'
            cv2.imwrite(temp_path, rotated_img)
            
            # Extract text
            text = processor.extract_text_from_image(temp_path)
            text_length = len(text.strip())
            quality = processor._calculate_text_quality(text)
            
            # Identify document
            doc_type = processor.identify_document_type(text)
            
            results.append({
                'angle': angle,
                'description': description,
                'length': text_length,
                'quality': quality,
                'doc_type': doc_type,
                'sample': text[:50].replace('\n', ' ')
            })
            
            print(f"📐 {description:8}: {text_length:3d} chars, quality: {quality:.2f}, type: {doc_type}")
            
            # Clean up
            os.remove(temp_path)
            
        except Exception as e:
            print(f"❌ Error with {description}: {e}")
    
    # Find best result
    if results:
        # Score by quality and length
        best_result = max(results, key=lambda x: x['quality'] * (1 + x['length'] / 100))
        
        print(f"\n🏆 Best orientation: {best_result['description']} ({best_result['angle']}°)")
        print(f"   📊 {best_result['length']} characters, quality: {best_result['quality']:.2f}")
        print(f"   🏷️  Document type: {best_result['doc_type']}")
        print(f"   📄 Sample: '{best_result['sample']}...'")

def main():
    test_orientation_detection()
    test_manual_rotations()

if __name__ == "__main__":
    main()
