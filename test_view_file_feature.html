<!DOCTYPE html>
<html>
<head>
    <title>View File Feature Demo</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .demo-card { border: 1px solid #ddd; padding: 20px; margin: 10px 0; border-radius: 8px; }
        .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        .view-btn { background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; }
        .view-btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>🎉 New Feature: "View File" Button Added!</h1>
    
    <div class="demo-card">
        <h2>✨ What's New:</h2>
        <ul>
            <li><strong>View File Button:</strong> Added to the top-right corner of each document card</li>
            <li><strong>Modal Popup:</strong> Click the button to view the original image in a popup</li>
            <li><strong>Download Option:</strong> Download the original file directly from the modal</li>
            <li><strong>Loading States:</strong> Shows loading spinner while image loads</li>
            <li><strong>Error Handling:</strong> Displays error message if image fails to load</li>
        </ul>
    </div>

    <div class="demo-card">
        <h2>🖼️ How It Works:</h2>
        <div class="header">
            <strong>ash-adhar.jpg</strong>
            <div>
                <button class="view-btn">👁️ View file</button>
                <span style="background: #28a745; color: white; padding: 4px 8px; border-radius: 4px; margin-left: 8px;">Aadhaar Card</span>
            </div>
        </div>
        <p><em>Example of how the button appears in each document card</em></p>
    </div>

    <div class="demo-card">
        <h2>🔧 Technical Implementation:</h2>
        <ul>
            <li><strong>Frontend:</strong> Bootstrap modal with responsive image display</li>
            <li><strong>Backend:</strong> New Flask route <code>/uploads/&lt;filename&gt;</code> to serve images</li>
            <li><strong>JavaScript:</strong> <code>viewFile(filename)</code> function handles modal display</li>
            <li><strong>Styling:</strong> Hover effects and smooth transitions</li>
        </ul>
    </div>

    <div class="demo-card">
        <h2>🎯 User Experience:</h2>
        <ol>
            <li>User uploads documents and sees extraction results</li>
            <li>Each document card now has a "View file" button in the top-right</li>
            <li>Clicking the button opens a modal with the original image</li>
            <li>User can view the full-size image and download if needed</li>
            <li>Modal can be closed by clicking the X or outside the modal</li>
        </ol>
    </div>

    <div class="demo-card" style="background: #f8f9fa;">
        <h2>🚀 Ready to Test!</h2>
        <p>The Flask application is now running with the new feature. Upload some documents and try clicking the "View file" button on any document card!</p>
        <p><strong>URL:</strong> <a href="http://localhost:5000" target="_blank">http://localhost:5000</a></p>
    </div>
</body>
</html>
