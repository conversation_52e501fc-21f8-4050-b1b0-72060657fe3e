from flask import Flask, request, render_template, jsonify, redirect, url_for, send_from_directory, abort
import os
import json
from werkzeug.utils import secure_filename
from document_processor import DocumentProcessor
from document_types import DocumentTypeManager

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Initialize processors
document_processor = DocumentProcessor()
doc_type_manager = DocumentTypeManager()

ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff', 'pdf'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_files():
    if 'files' not in request.files:
        return jsonify({'error': 'No files selected'}), 400
    
    files = request.files.getlist('files')
    if not files or all(file.filename == '' for file in files):
        return jsonify({'error': 'No files selected'}), 400
    
    results = []
    
    for file in files:
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)
            
            # Process the document
            result = document_processor.process_document(filepath, filename)
            results.append(result)
    
    # Group results by person/entity
    grouped_results = group_documents_by_person(results)
    
    return jsonify(grouped_results)

@app.route('/document-types')
def get_document_types():
    return jsonify(doc_type_manager.get_all_types())

@app.route('/add-document-type', methods=['POST'])
def add_document_type():
    data = request.get_json()
    doc_type_manager.add_new_type(data['name'], data['patterns'])
    return jsonify({'success': True})

@app.route('/learn-document', methods=['POST'])
def learn_document():
    data = request.get_json()
    filename = data['filename']
    doc_type = data['document_type']
    extracted_data = data['extracted_data']

    # Learn from this document for future recognition
    doc_type_manager.learn_from_document(filename, doc_type, extracted_data)

    return jsonify({'success': True})

@app.route('/manual-classify', methods=['POST'])
def manual_classify():
    """Manually classify a document and re-extract data"""
    data = request.get_json()
    filename = data['filename']
    doc_type = data['document_type']
    raw_text = data['raw_text']

    # Re-process with the specified document type
    structured_data = document_processor.extract_structured_data(raw_text, doc_type)

    # Create result
    result = {
        'filename': filename,
        'Document Type': doc_type,
        'Raw Text': raw_text
    }
    result.update(structured_data)

    # Learn from this manual classification
    doc_type_manager.learn_from_document(filename, doc_type, structured_data, raw_text)

    return jsonify(result)

@app.route('/reprocess-image', methods=['POST'])
def reprocess_image():
    """Reprocess image with different preprocessing method"""
    if 'file' not in request.files:
        return jsonify({'error': 'No file provided'}), 400

    file = request.files['file']
    method = request.form.get('method', 'aggressive')

    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)

        # Process with specific method
        document_processor_temp = DocumentProcessor()
        raw_text = document_processor_temp.extract_text_from_image(filepath)

        # Try to identify document type
        doc_type = document_processor_temp.identify_document_type(raw_text)

        result = {
            'filename': filename,
            'Document Type': doc_type,
            'Raw Text': raw_text,
            'Processing Method': method
        }

        if doc_type != 'Unknown':
            structured_data = document_processor_temp.extract_structured_data(raw_text, doc_type)
            result.update(structured_data)
        else:
            suggestions = document_processor_temp.get_document_suggestions(raw_text)
            result['Suggestions'] = suggestions
            result['Manual_Classification_Needed'] = True

        return jsonify(result)

    return jsonify({'error': 'Invalid file'}), 400

@app.route('/reprocess-existing', methods=['POST'])
def reprocess_existing():
    """Reprocess an existing uploaded file with different preprocessing method"""
    data = request.get_json()
    filename = data.get('filename')
    method = data.get('method', 'aggressive')

    if not filename:
        return jsonify({'error': 'No filename provided'}), 400

    # Check if file exists
    filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    if not os.path.exists(filepath):
        return jsonify({'error': 'File not found on server'}), 404

    try:
        # Process with specific method
        document_processor_temp = DocumentProcessor()
        raw_text = document_processor_temp.extract_text_from_image(filepath)

        # Try to identify document type
        doc_type = document_processor_temp.identify_document_type(raw_text)

        result = {
            'filename': filename,
            'Document Type': doc_type,
            'Raw Text': raw_text,
            'Processing Method': method
        }

        if doc_type != 'Unknown':
            structured_data = document_processor_temp.extract_structured_data(raw_text, doc_type)
            result.update(structured_data)
        else:
            suggestions = document_processor_temp.get_document_suggestions(raw_text)
            result['Suggestions'] = suggestions
            result['Manual_Classification_Needed'] = True

        return jsonify(result)
    except Exception as e:
        return jsonify({'error': f'Error processing file: {str(e)}'}), 500

def normalize_name_for_grouping(name):
    """Normalize name for grouping purposes"""
    if not name or name == "Unknown":
        return name

    # Remove apostrophes and dots first, then normalize spaces
    cleaned = name.replace("'", "").replace(".", "")

    # Convert to title case and remove extra spaces
    normalized = ' '.join(cleaned.strip().title().split())

    return normalized

def group_documents_by_person(results):
    """Group documents by person/entity name with intelligent name matching"""
    grouped = {}
    name_mapping = {}  # Maps normalized names to display names

    for result in results:
        # Try to find a person identifier (name)
        person_name = "Unknown"

        if result.get('Document Type') != 'Unknown':
            # Look for name fields
            if 'Name' in result:
                person_name = result['Name']
            elif 'Father\'s Name' in result:
                person_name = result['Father\'s Name']

        # Normalize the name for grouping
        normalized_name = normalize_name_for_grouping(person_name)

        # Use the normalized name as the key, but keep track of the best display name
        if normalized_name not in grouped:
            grouped[normalized_name] = {
                "documents": []
            }
            # Use the first occurrence as the display name, preferring title case
            name_mapping[normalized_name] = person_name
        else:
            # Update display name if current one is better (title case preferred over all caps)
            current_display = name_mapping[normalized_name]
            if (person_name != "Unknown" and
                (current_display.isupper() and not person_name.isupper())):
                name_mapping[normalized_name] = person_name

        grouped[normalized_name]["documents"].append(result)
    
    # Convert to the required format using display names
    formatted_result = []
    for normalized_name, data in grouped.items():
        display_name = name_mapping.get(normalized_name, normalized_name)
        formatted_result.append({display_name: data})

    return formatted_result

@app.route('/uploads/<filename>')
def uploaded_file(filename):
    """Serve uploaded files for viewing in the modal"""
    try:
        return send_from_directory(app.config['UPLOAD_FOLDER'], filename)
    except FileNotFoundError:
        abort(404)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
