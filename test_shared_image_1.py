#!/usr/bin/env python3
"""
Test shared_image_1.jpg with enhanced orientation detection
"""

import os
from document_processor import DocumentProcessor

def test_shared_image_1():
    """Test shared_image_1.jpg processing"""
    
    processor = DocumentProcessor()
    
    test_image = 'uploads/shared_image_1.jpg'
    
    if not os.path.exists(test_image):
        print(f"❌ Test image not found: {test_image}")
        return
    
    print("🔍 Testing shared_image_1.jpg with Enhanced Processing")
    print("=" * 70)
    
    try:
        # Extract text with orientation correction
        print("📄 Extracting text with orientation detection...")
        text = processor.extract_text_from_image(test_image)
        text_length = len(text.strip())
        quality = processor._calculate_text_quality(text)
        
        print(f"\n📊 Results:")
        print(f"   Text length: {text_length} characters")
        print(f"   Quality score: {quality:.2f}")
        print(f"   Sample text: '{text[:150].replace(chr(10), ' ')}...'")
        
        # Test document identification
        print(f"\n🏷️  Document identification:")
        doc_type = processor.identify_document_type(text)
        print(f"   Identified as: {doc_type}")
        
        if doc_type != 'Unknown':
            # Get suggestions for debugging
            suggestions = processor.get_document_suggestions(text)
            print(f"   Confidence details:")
            for suggestion in suggestions['possible_types']:
                print(f"     - {suggestion['type']}: {suggestion['confidence']:.2f}")
        
        # Test structured data extraction
        if doc_type != 'Unknown':
            print(f"\n📊 Extracting structured data...")
            structured_data = processor.extract_structured_data(text, doc_type)
            
            if structured_data:
                print(f"✅ Extracted {len(structured_data)} fields:")
                for key, value in structured_data.items():
                    if key == 'Quality_Remarks':
                        print(f"   ⚠️  {key}: {value}")
                    elif key == 'Raw Text':
                        print(f"   📄 {key}: '{str(value)[:100]}...'")
                    else:
                        print(f"   📋 {key}: {value}")
            else:
                print(f"❌ No structured data extracted")
        else:
            print(f"\n⚠️  Document type unknown - checking suggestions...")
            suggestions = processor.get_document_suggestions(text)
            if suggestions['possible_types']:
                print(f"💡 Suggestions:")
                for suggestion in suggestions['possible_types']:
                    print(f"   - {suggestion['type']}: confidence {suggestion['confidence']:.2f}")
                    print(f"     Keywords: {suggestion.get('keywords_found', [])}")
                    print(f"     Patterns: {suggestion.get('patterns_found', [])}")
            else:
                print(f"❌ No document type suggestions")
        
        # Overall assessment
        print(f"\n🎯 Assessment:")
        if doc_type != 'Unknown':
            print(f"✅ SUCCESS: Document correctly identified and processed")
        elif text_length > 100:
            print(f"⚠️  PARTIAL: Text extracted but document type unclear")
            print(f"   This might be due to poor image quality or unusual formatting")
        else:
            print(f"❌ FAILED: Insufficient text extracted")
            print(f"   Image might be too poor quality or incorrectly oriented")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def main():
    test_shared_image_1()

if __name__ == "__main__":
    main()
