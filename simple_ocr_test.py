#!/usr/bin/env python3
"""
Simple OCR test to check if Tesseract is working
"""

import pytesseract
import cv2
import os

def test_simple_ocr():
    """Test basic OCR functionality"""
    
    image_path = "uploads/shared_image.jpg"
    
    if not os.path.exists(image_path):
        print(f"❌ Image file not found: {image_path}")
        return
    
    print(f"🔍 Testing simple OCR on: {image_path}")
    
    try:
        # Load image
        print("📷 Loading image...")
        image = cv2.imread(image_path)
        
        if image is None:
            print("❌ Could not load image")
            return
            
        print(f"✅ Image loaded: {image.shape}")
        
        # Convert to grayscale
        print("🔄 Converting to grayscale...")
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Simple OCR test
        print("📝 Running basic OCR...")
        text = pytesseract.image_to_string(gray)
        
        print(f"📄 Extracted text ({len(text)} characters):")
        print("=" * 50)
        print(text)
        print("=" * 50)
        
        # Check for PAN patterns
        import re
        pan_pattern = r'[A-Z]{5}[0-9]{4}[A-Z]'
        pan_matches = re.findall(pan_pattern, text)
        
        if pan_matches:
            print(f"🎯 Found PAN numbers: {pan_matches}")
        else:
            print("⚠️  No PAN pattern found in extracted text")
            
    except Exception as e:
        print(f"❌ Error during OCR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple_ocr()
