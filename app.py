from flask import Flask, request, render_template, jsonify, redirect, url_for, send_from_directory, abort
import os
import json
import cv2
from werkzeug.utils import secure_filename
from document_processor import DocumentProcessor
from document_types import DocumentTypeManager

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Initialize processors
document_processor = DocumentProcessor()
doc_type_manager = DocumentTypeManager()

ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff', 'pdf'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_files():
    if 'files' not in request.files:
        return jsonify({'error': 'No files selected'}), 400
    
    files = request.files.getlist('files')
    if not files or all(file.filename == '' for file in files):
        return jsonify({'error': 'No files selected'}), 400
    
    results = []
    
    for file in files:
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)
            
            # Process the document
            result = document_processor.process_document(filepath, filename)
            results.append(result)
    
    # Group results by person/entity
    grouped_results = group_documents_by_person(results)
    
    return jsonify(grouped_results)

@app.route('/document-types')
def get_document_types():
    return jsonify(doc_type_manager.get_all_types())

@app.route('/add-document-type', methods=['POST'])
def add_document_type():
    data = request.get_json()
    doc_type_manager.add_new_type(data['name'], data['patterns'])
    return jsonify({'success': True})

@app.route('/learn-document', methods=['POST'])
def learn_document():
    data = request.get_json()
    filename = data['filename']
    doc_type = data['document_type']
    extracted_data = data['extracted_data']

    # Learn from this document for future recognition
    doc_type_manager.learn_from_document(filename, doc_type, extracted_data)

    return jsonify({'success': True})

@app.route('/manual-classify', methods=['POST'])
def manual_classify():
    """Manually classify a document and re-extract data"""
    data = request.get_json()
    filename = data['filename']
    doc_type = data['document_type']
    raw_text = data['raw_text']

    # Re-process with the specified document type
    structured_data = document_processor.extract_structured_data(raw_text, doc_type)

    # Create result
    result = {
        'filename': filename,
        'Document Type': doc_type,
        'Raw Text': raw_text
    }
    result.update(structured_data)

    # Learn from this manual classification
    doc_type_manager.learn_from_document(filename, doc_type, structured_data, raw_text)

    return jsonify(result)

@app.route('/reprocess-image', methods=['POST'])
def reprocess_image():
    """Reprocess image with different preprocessing method"""
    if 'file' not in request.files:
        return jsonify({'error': 'No file provided'}), 400

    file = request.files['file']
    method = request.form.get('method', 'aggressive')

    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)

        # Process with specific method
        document_processor_temp = DocumentProcessor()
        raw_text = document_processor_temp.extract_text_from_image(filepath)

        # Try to identify document type
        doc_type = document_processor_temp.identify_document_type(raw_text)

        result = {
            'filename': filename,
            'Document Type': doc_type,
            'Raw Text': raw_text,
            'Processing Method': method
        }

        if doc_type != 'Unknown':
            structured_data = document_processor_temp.extract_structured_data(raw_text, doc_type)
            result.update(structured_data)
        else:
            suggestions = document_processor_temp.get_document_suggestions(raw_text)
            result['Suggestions'] = suggestions
            result['Manual_Classification_Needed'] = True

        return jsonify(result)

    return jsonify({'error': 'Invalid file'}), 400

@app.route('/reprocess-existing', methods=['POST'])
def reprocess_existing():
    """Reprocess an existing uploaded file with different preprocessing method"""
    data = request.get_json()
    filename = data.get('filename')
    method = data.get('method', 'aggressive')

    if not filename:
        return jsonify({'error': 'No filename provided'}), 400

    # Check if file exists
    filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    if not os.path.exists(filepath):
        return jsonify({'error': 'File not found on server'}), 404

    try:
        # Process with specific method
        document_processor_temp = DocumentProcessor()
        raw_text = document_processor_temp.extract_text_from_image(filepath)

        # Try to identify document type
        doc_type = document_processor_temp.identify_document_type(raw_text)

        result = {
            'filename': filename,
            'Document Type': doc_type,
            'Raw Text': raw_text,
            'Processing Method': method
        }

        if doc_type != 'Unknown':
            structured_data = document_processor_temp.extract_structured_data(raw_text, doc_type)
            result.update(structured_data)
        else:
            suggestions = document_processor_temp.get_document_suggestions(raw_text)
            result['Suggestions'] = suggestions
            result['Manual_Classification_Needed'] = True

        return jsonify(result)
    except Exception as e:
        return jsonify({'error': f'Error processing file: {str(e)}'}), 500

@app.route('/auto-enhance', methods=['POST'])
def auto_enhance():
    """Automatically try all processing methods until we get a good result"""
    data = request.get_json()
    filename = data.get('filename')

    if not filename:
        return jsonify({'error': 'No filename provided'}), 400

    # Check if file exists
    filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    if not os.path.exists(filepath):
        return jsonify({'error': 'File not found on server'}), 404

    try:
        # Use the existing reprocess methods that we know work
        methods = [
            'gentle',
            'standard',
            'aggressive',
            'poor_quality',
            'extreme_enhancement',
            'noise_reduction'
        ]

        best_result = None
        best_score = 0
        method_used = None

        for method in methods:
            try:
                print(f"Trying method: {method}")

                # Use the existing reprocess endpoint logic
                document_processor_temp = DocumentProcessor()
                raw_text = document_processor_temp.extract_text_from_image(filepath)

                # Try to identify document type
                doc_type = document_processor_temp.identify_document_type(raw_text)

                print(f"Method {method}: doc_type={doc_type}, text_length={len(raw_text) if raw_text else 0}")

                # Calculate quality score
                quality_score = calculate_extraction_quality(raw_text, doc_type)

                result = {
                    'filename': filename,
                    'Document Type': doc_type,
                    'Raw Text': raw_text,
                    'Processing Method': method,
                    'Quality Score': quality_score
                }

                if doc_type != 'Unknown':
                    structured_data = document_processor_temp.extract_structured_data(raw_text, doc_type)
                    result.update(structured_data)
                else:
                    suggestions = document_processor_temp.get_document_suggestions(raw_text)
                    result['Suggestions'] = suggestions
                    result['Manual_Classification_Needed'] = True

                # If we found a good result, use it
                if quality_score > best_score:
                    best_score = quality_score
                    best_result = result
                    method_used = method

                print(f"Current best score: {best_score} with method: {method_used}")

                # If we got a very good result, stop trying more methods
                if quality_score > 80 and doc_type != 'Unknown':
                    print(f"Excellent result found with {method}, stopping early")
                    break

                # If we got a decent result with known document type, stop
                if quality_score > 60 and doc_type != 'Unknown':
                    print(f"Good result found with {method}, stopping early")
                    break

            except Exception as e:
                print(f"Method {method} failed: {e}")
                continue

        if best_result:
            best_result['auto_enhancement_used'] = True
            best_result['methods_tried'] = methods[:methods.index(method_used) + 1] if method_used else methods
            print(f"Returning best result with score: {best_score}")
            return jsonify(best_result)
        else:
            return jsonify({'error': 'All enhancement methods failed'}), 500

    except Exception as e:
        print(f"Auto-enhance error: {e}")
        return jsonify({'error': f'Error during auto-enhancement: {str(e)}'}), 500

def calculate_extraction_quality(raw_text, doc_type):
    """Calculate quality score for extracted text"""
    if not raw_text or not raw_text.strip():
        return 0

    score = 0
    text_length = len(raw_text.strip())

    # Base score from text length
    score += min(text_length / 10, 30)  # Max 30 points for length

    # Bonus for document type identification
    if doc_type != 'Unknown':
        score += 40  # 40 points for successful classification

    # Bonus for structured patterns (PAN, Aadhaar, etc.)
    import re
    patterns = {
        'PAN': r'[A-Z]{5}[0-9]{4}[A-Z]',
        'Aadhaar': r'\d{4}\s*\d{4}\s*\d{4}',
        'Date': r'\d{1,2}[/-]\d{1,2}[/-]\d{4}',
        'Name': r'[A-Z][a-z]+\s+[A-Z][a-z]+',
    }

    for pattern_name, pattern in patterns.items():
        if re.search(pattern, raw_text):
            score += 10  # 10 points per pattern found

    # Penalty for too much noise (special characters)
    noise_chars = len(re.findall(r'[^a-zA-Z0-9\s\n\r\t.,:/()-]', raw_text))
    noise_ratio = noise_chars / max(text_length, 1)
    score -= noise_ratio * 20  # Reduce score for noise

    return max(0, min(100, score))  # Clamp between 0-100

def normalize_name_for_grouping(name):
    """Normalize name for grouping purposes"""
    if not name or name == "Unknown":
        return name

    # Remove apostrophes and dots first, then normalize spaces
    cleaned = name.replace("'", "").replace(".", "")

    # Convert to title case and remove extra spaces
    normalized = ' '.join(cleaned.strip().title().split())

    return normalized

def group_documents_by_person(results):
    """Group documents by person/entity name with intelligent name matching for both individual and company documents"""
    grouped = {}
    name_mapping = {}  # Maps normalized names to display names

    # Initialize document type manager to get document categories
    doc_type_manager = DocumentTypeManager()

    for result in results:
        # Determine if this is a company or individual document
        doc_type = result.get('Document Type', 'Unknown')
        entity_name = "Unknown"
        entity_category = "individual"  # default

        # Get document category from document types
        if doc_type in doc_type_manager.document_types:
            entity_category = doc_type_manager.document_types[doc_type].get('category', 'individual')

        if result.get('Document Type') != 'Unknown':
            if entity_category == 'company':
                # For company documents, look for company-specific name fields
                if 'Company Name' in result:
                    entity_name = result['Company Name']
                elif 'Firm Name' in result:
                    entity_name = result['Firm Name']
                elif 'Trust Name' in result:
                    entity_name = result['Trust Name']
                elif 'Society Name' in result:
                    entity_name = result['Society Name']
                elif 'Account Holder' in result:  # For cancelled cheque
                    entity_name = result['Account Holder']
                elif 'Name' in result:
                    entity_name = result['Name']
            else:
                # For individual documents, look for person name fields
                if 'Name' in result:
                    entity_name = result['Name']
                elif 'Father\'s Name' in result:
                    entity_name = result['Father\'s Name']

        # Normalize the name for grouping
        normalized_name = normalize_name_for_grouping(entity_name)

        # Create a unique key that includes category to separate companies from individuals
        group_key = f"{entity_category}_{normalized_name}"

        # Use the group key, but keep track of the best display name
        if group_key not in grouped:
            grouped[group_key] = {
                "documents": [],
                "category": entity_category
            }
            # Use the first occurrence as the display name, preferring title case
            name_mapping[group_key] = entity_name
        else:
            # Update display name if current one is better (title case preferred over all caps)
            current_display = name_mapping[group_key]
            if (entity_name != "Unknown" and
                (current_display.isupper() and not entity_name.isupper())):
                name_mapping[group_key] = entity_name

        grouped[group_key]["documents"].append(result)
    
    # Convert to the required format using display names and add category icons
    formatted_result = []
    for group_key, data in grouped.items():
        display_name = name_mapping.get(group_key, group_key.split('_', 1)[1])  # Remove category prefix
        category = data['category']

        # Add category icon to display name
        if category == 'company':
            display_name_with_icon = f"🏢 {display_name}"
        else:
            display_name_with_icon = f"👤 {display_name}"

        # Remove category from data before adding to result
        result_data = {
            "documents": data["documents"],
            "category": category
        }

        formatted_result.append({display_name_with_icon: result_data})

    return formatted_result

@app.route('/uploads/<filename>')
def uploaded_file(filename):
    """Serve uploaded files for viewing in the modal"""
    try:
        return send_from_directory(app.config['UPLOAD_FOLDER'], filename)
    except FileNotFoundError:
        abort(404)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
