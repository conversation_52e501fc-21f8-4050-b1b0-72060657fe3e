import pytesseract
from PIL import Image
import cv2
import numpy as np
import re
import os
from document_types import DocumentTypeManager

class DocumentProcessor:
    def __init__(self):
        self.doc_type_manager = DocumentTypeManager()
        # Configure tesseract path for Windows
        import platform
        if platform.system() == 'Windows':
            # Common Tesseract installation paths on Windows
            possible_paths = [
                r'C:\Program Files\Tesseract-OCR\tesseract.exe',
                r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
                r'C:\Users\<USER>\AppData\Local\Tesseract-OCR\tesseract.exe'.format(os.environ.get('USERNAME', '')),
                r'C:\tesseract\tesseract.exe'
            ]

            for path in possible_paths:
                if os.path.exists(path):
                    pytesseract.pytesseract.tesseract_cmd = path
                    print(f"Found Tesseract at: {path}")
                    break
            else:
                print("Warning: Tesseract not found in common locations. Please set the path manually.")
                # Try to use tesseract from PATH
                try:
                    import subprocess
                    result = subprocess.run(['tesseract', '--version'], capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        print("Using Tesseract from system PATH")
                    else:
                        print("Tesseract not available in PATH either")
                except Exception as e:
                    print(f"Could not find Tesseract in PATH: {e}")
    
    def preprocess_image(self, image_path, method='standard'):
        """Preprocess image for better OCR results with multiple methods"""
        # Read image
        img = cv2.imread(image_path)

        # First, try to detect and correct orientation
        img = self._detect_and_correct_orientation(img)

        if method == 'aggressive':
            return self._aggressive_preprocessing(img)
        elif method == 'gentle':
            return self._gentle_preprocessing(img)
        elif method == 'pan_specific':
            return self._pan_specific_preprocessing(img)
        elif method == 'poor_quality':
            return self._poor_quality_preprocessing(img)
        elif method == 'extreme_enhancement':
            return self._extreme_enhancement_preprocessing(img)
        elif method == 'noise_reduction':
            return self._noise_reduction_preprocessing(img)
        elif method == 'skew_correction':
            return self._skew_correction_preprocessing(img)
        elif method == 'perspective_correction':
            return self._perspective_correction_preprocessing(img)
        elif method == 'super_enhancement':
            return self._super_enhancement_preprocessing(img)
        elif method == 'multi_approach':
            return self._multi_approach_preprocessing(img)
        else:
            return self._standard_preprocessing(img)

    def _standard_preprocessing(self, img):
        """Standard preprocessing method"""
        # Resize image if too small
        height, width = img.shape[:2]
        if height < 600 or width < 800:
            scale_factor = max(800/width, 600/height)
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            img = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # Apply CLAHE for better contrast
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)

        # Apply noise reduction
        denoised = cv2.medianBlur(enhanced, 3)

        # Apply threshold
        _, thresh = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        return thresh

    def _gentle_preprocessing(self, img):
        """Gentle preprocessing for high-quality images"""
        # Minimal processing for clear images
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # Slight enhancement
        clahe = cv2.createCLAHE(clipLimit=1.5, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)

        return enhanced

    def _aggressive_preprocessing(self, img):
        """Aggressive preprocessing for poor quality images"""
        # Resize significantly if small
        height, width = img.shape[:2]
        if height < 800 or width < 1000:
            scale_factor = max(1000/width, 800/height)
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            img = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # Strong contrast enhancement
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)

        # Multiple noise reduction steps
        denoised = cv2.medianBlur(enhanced, 5)
        denoised = cv2.bilateralFilter(denoised, 9, 75, 75)

        # Sharpening
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(denoised, -1, kernel)

        # Adaptive threshold
        thresh = cv2.adaptiveThreshold(sharpened, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                     cv2.THRESH_BINARY, 11, 2)

        # Morphological operations
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        processed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        processed = cv2.morphologyEx(processed, cv2.MORPH_OPEN, kernel)

        return processed

    def _pan_specific_preprocessing(self, img):
        """PAN card specific preprocessing"""
        # PAN cards often have specific color schemes and layouts
        # Resize for better OCR
        height, width = img.shape[:2]
        if height < 800 or width < 1200:
            scale_factor = max(1200/width, 800/height)
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            img = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # PAN cards often have blue/green backgrounds, enhance contrast
        clahe = cv2.createCLAHE(clipLimit=2.5, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)

        # Noise reduction
        denoised = cv2.medianBlur(enhanced, 3)

        # Adaptive threshold works well for PAN cards
        thresh = cv2.adaptiveThreshold(denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                     cv2.THRESH_BINARY, 11, 2)

        # Clean up
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 1))
        processed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)

        return processed

    def _poor_quality_preprocessing(self, img):
        """Preprocessing specifically for poor quality images"""
        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # Resize image to improve OCR (make it larger)
        height, width = gray.shape
        scale_factor = max(2.0, 1500 / max(height, width))
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)
        resized = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

        # Strong noise reduction
        denoised = cv2.bilateralFilter(resized, 9, 75, 75)

        # Enhance contrast using CLAHE
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        enhanced = clahe.apply(denoised)

        # Multiple threshold attempts
        # Try Otsu's thresholding
        _, thresh1 = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # Try adaptive threshold
        thresh2 = cv2.adaptiveThreshold(enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                       cv2.THRESH_BINARY, 15, 10)

        # Combine both thresholds
        combined = cv2.bitwise_and(thresh1, thresh2)

        # Morphological operations to clean up
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        cleaned = cv2.morphologyEx(combined, cv2.MORPH_CLOSE, kernel)
        cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel)

        return cleaned

    def _extreme_enhancement_preprocessing(self, img):
        """Extreme enhancement for very poor quality images"""
        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # Aggressive upscaling
        height, width = gray.shape
        scale_factor = max(3.0, 2000 / max(height, width))
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)
        resized = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)

        # Multiple denoising passes
        denoised = cv2.fastNlMeansDenoising(resized, None, 10, 7, 21)
        denoised = cv2.medianBlur(denoised, 3)

        # Extreme contrast enhancement
        clahe = cv2.createCLAHE(clipLimit=4.0, tileGridSize=(4,4))
        enhanced = clahe.apply(denoised)

        # Sharpening
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(enhanced, -1, kernel)

        # Adaptive threshold with larger neighborhood
        thresh = cv2.adaptiveThreshold(sharpened, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                     cv2.THRESH_BINARY, 21, 15)

        return thresh

    def _noise_reduction_preprocessing(self, img):
        """Focus on noise reduction for scanned documents"""
        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # Moderate upscaling
        height, width = gray.shape
        scale_factor = max(1.5, 1200 / max(height, width))
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)
        resized = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

        # Advanced denoising
        denoised = cv2.fastNlMeansDenoising(resized, None, 8, 7, 21)

        # Gaussian blur to smooth
        blurred = cv2.GaussianBlur(denoised, (3, 3), 0)

        # Enhance contrast
        enhanced = cv2.convertScaleAbs(blurred, alpha=1.2, beta=10)

        # Threshold
        _, thresh = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        return thresh

    def clean_mixed_language_text(self, text):
        """Clean mixed Hindi/English OCR text to extract English content"""
        import re

        # Split into lines
        lines = text.split('\n')
        cleaned_lines = []

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Remove Hindi characters (Devanagari script)
            # Keep only English characters, numbers, and common punctuation
            english_only = re.sub(r'[^\x00-\x7F]+', ' ', line)

            # Clean up extra spaces
            english_only = re.sub(r'\s+', ' ', english_only).strip()

            # Skip lines that are too short or just punctuation
            if len(english_only) > 2 and not re.match(r'^[^\w]*$', english_only):
                cleaned_lines.append(english_only)

        return '\n'.join(cleaned_lines)

    def _calculate_text_quality(self, text):
        """Calculate quality score for extracted text"""
        if not text or len(text.strip()) == 0:
            return 0.0

        # Count different types of characters
        total_chars = len(text)
        alphanumeric_chars = sum(1 for c in text if c.isalnum())
        letter_chars = sum(1 for c in text if c.isalpha())
        digit_chars = sum(1 for c in text if c.isdigit())
        space_chars = sum(1 for c in text if c.isspace())

        # Calculate ratios
        alphanumeric_ratio = alphanumeric_chars / total_chars if total_chars > 0 else 0
        letter_ratio = letter_chars / total_chars if total_chars > 0 else 0

        # Penalize text with too many special characters or gibberish
        if alphanumeric_ratio < 0.3:  # Less than 30% alphanumeric
            return 0.1

        # Reward text with good letter content
        quality_score = alphanumeric_ratio * 0.7 + letter_ratio * 0.3

        # Bonus for having both letters and numbers (typical in ID documents)
        if letter_chars > 0 and digit_chars > 0:
            quality_score += 0.1

        # Bonus for reasonable length
        if 50 <= len(text.strip()) <= 1000:
            quality_score += 0.1

        return min(1.0, quality_score)

    def get_optimal_psm_modes(self, image_path):
        """Determine optimal PSM modes based on image characteristics"""
        try:
            # Analyze image to determine best PSM modes
            img = cv2.imread(image_path)
            height, width = img.shape[:2]
            aspect_ratio = width / height

            # Different PSM modes for different document types and layouts
            psm_modes = []

            # For card-like documents (PAN, Aadhaar) - typically wider than tall
            if aspect_ratio > 1.4:  # Landscape orientation
                psm_modes.extend([
                    '--psm 6',   # Uniform block of text (good for cards)
                    '--psm 4',   # Single column of text
                    '--psm 3',   # Fully automatic page segmentation
                    '--psm 8',   # Single word
                    '--psm 7',   # Single text line
                ])
            # For portrait documents (licenses, passports)
            elif aspect_ratio < 0.8:  # Portrait orientation
                psm_modes.extend([
                    '--psm 4',   # Single column of text
                    '--psm 3',   # Fully automatic page segmentation
                    '--psm 6',   # Uniform block of text
                    '--psm 1',   # Automatic page segmentation with OSD
                ])
            # For square-ish documents
            else:
                psm_modes.extend([
                    '--psm 3',   # Fully automatic page segmentation
                    '--psm 6',   # Uniform block of text
                    '--psm 4',   # Single column of text
                    '--psm 8',   # Single word
                ])

            return psm_modes

        except Exception as e:
            print(f"Error analyzing image for PSM selection: {e}")
            # Fallback to comprehensive list
            return ['--psm 3', '--psm 6', '--psm 4', '--psm 8', '--psm 7', '--psm 1']

    def extract_text_from_image(self, image_path):
        """Extract text from image using adaptive preprocessing and PSM selection"""
        # Reset orientation check for new image
        if hasattr(self, '_orientation_checked'):
            delattr(self, '_orientation_checked')

        try:
            best_text = ""
            best_length = 0
            best_method = ""

            # Get optimal PSM modes for this image
            optimal_psm_modes = self.get_optimal_psm_modes(image_path)

            # Comprehensive method combinations
            preprocessing_methods = [
                'original_eng_hin',     # Original image with mixed languages
                'original_eng',         # Original image English only
                'gentle',              # Minimal preprocessing
                'standard',            # Standard preprocessing
                'aggressive',          # Heavy preprocessing
                'pan_specific',        # PAN card specific
                'poor_quality',        # Poor quality image enhancement
                'extreme_enhancement', # Extreme enhancement for very poor images
                'noise_reduction',     # Focus on noise reduction
                'skew_correction',     # Skew correction for tilted images
                'perspective_correction', # Perspective correction for angled photos
                'super_enhancement',   # Super enhancement for extremely poor quality
                'multi_approach',      # Multi-approach (tries all methods, picks best)
            ]

            # Try each preprocessing method with optimal PSM modes
            for method in preprocessing_methods:
                for psm_config in optimal_psm_modes:
                    try:
                        # Handle different preprocessing methods
                        if method == 'original_eng_hin':
                            # Use original image without preprocessing, mixed languages
                            pil_img = Image.open(image_path)
                            text = pytesseract.image_to_string(pil_img, lang='eng+hin', config=psm_config)
                        elif method == 'original_eng':
                            # Use original image without preprocessing, English only
                            pil_img = Image.open(image_path)
                            text = pytesseract.image_to_string(pil_img, lang='eng', config=psm_config)
                        else:
                            # Preprocess image with current method
                            processed_img = self.preprocess_image(image_path, method=method)
                            pil_img = Image.fromarray(processed_img)
                            text = pytesseract.image_to_string(pil_img, lang='eng', config=psm_config)

                        # Clean and evaluate the text
                        cleaned_text = self.clean_mixed_language_text(text)
                        text_length = len(cleaned_text.strip())

                        # Quality scoring: prefer results with more alphanumeric content
                        quality_score = self._calculate_text_quality(cleaned_text)
                        combined_score = text_length * quality_score

                        # Keep the best result based on combined score
                        if combined_score > best_length:
                            best_text = cleaned_text
                            best_length = combined_score
                            best_method = f"{method} + {psm_config}"

                        # Early exit if we get very good results
                        if text_length > 200 and quality_score > 0.7:
                            print(f"Early exit with good result: {text_length} chars, quality: {quality_score:.2f}")
                            break

                    except Exception as e:
                        print(f"Method {method} with {psm_config} failed: {e}")
                        continue

                # Break outer loop if we found good results
                if best_length > 200 * 0.7:  # Good length * quality threshold
                    break

            # Try original image with different PSM modes if preprocessing didn't work well
            if best_length < 50:  # Very poor results, try fallback
                fallback_psm_modes = ['--psm 3', '--psm 6', '--psm 4', '--psm 8']
                for psm_mode in fallback_psm_modes:
                    try:
                        original_img = Image.open(image_path)
                        original_text = pytesseract.image_to_string(original_img, lang='eng', config=psm_mode)
                        cleaned_fallback = self.clean_mixed_language_text(original_text)
                        fallback_quality = self._calculate_text_quality(cleaned_fallback)
                        fallback_score = len(cleaned_fallback.strip()) * fallback_quality

                        if fallback_score > best_length:
                            best_text = cleaned_fallback
                            best_method = f"fallback original + {psm_mode}"
                            best_length = fallback_score
                    except Exception as e:
                        print(f"Fallback method {psm_mode} failed: {e}")
                        continue

            # Convert score back to length for display
            actual_length = len(best_text.strip()) if best_text else 0
            print(f"Best OCR result: {actual_length} characters (score: {best_length:.1f}) using {best_method}")

            return best_text.strip() if best_text else ""

        except Exception as e:
            print(f"Error extracting text from {image_path}: {str(e)}")
            return ""
    
    def identify_document_type(self, text):
        """Identify document type with enhanced Aadhaar detection and poor quality handling"""
        doc_type, confidence = self.doc_type_manager.identify_document_type(text)

        # Enhanced Aadhaar detection for poor quality images
        if doc_type == 'Unknown':
            doc_type = self._enhanced_aadhaar_detection(text)

        return doc_type

    def _enhanced_aadhaar_detection(self, text):
        """Enhanced Aadhaar card detection for poor quality images"""
        aadhaar_indicators = 0
        text_lower = text.lower()

        # Strong Aadhaar indicators
        strong_indicators = [
            'government of india',
            'govt of india',
            'unique identification',
            'uidai',
            'aadhaar',
            'aadhar',
            'आधार',
            'male',
            'female',
            'income tax department',
            'permanent account number',
            'dob',
            'date of birth',
        ]

        # Pattern indicators - enhanced for better detection
        patterns = [
            r'\b\d{4}\s*\d{4}\s*\d{4}\b',  # 12-digit number pattern
            r'dob\s*:?\s*\d{1,2}[/-]\d{1,2}[/-]\d{4}',  # DOB pattern
            r'\b(male|female)\b',  # Gender
            r'address\s*:',  # Address field
            r'\b\d{2}/\d{2}/\d{4}\b',  # Date pattern
            r'[A-Z]{2,}\s+[A-Z]{2,}\s+[A-Z]{2,}',  # Multiple uppercase words (names)
            r's/o\s+|d/o\s+|w/o\s+',  # Son/Daughter/Wife of patterns
        ]

        # Check strong indicators
        for indicator in strong_indicators:
            if indicator in text_lower:
                aadhaar_indicators += 2

        # Check patterns
        import re
        for pattern in patterns:
            if re.search(pattern, text_lower):
                aadhaar_indicators += 1

        # Additional checks for specific Aadhaar characteristics
        # Check for QR code presence (common in Aadhaar)
        if 'qr' in text_lower or len(re.findall(r'[█▀▄▌▐░▒▓]', text)) > 10:
            aadhaar_indicators += 1

        # Check for typical Aadhaar layout patterns
        if re.search(r'[A-Z]{3,}\s+[A-Z]{3,}\s+[A-Z]{3,}', text):  # Multiple caps words
            aadhaar_indicators += 1

        # If we have enough indicators, classify as Aadhaar
        if aadhaar_indicators >= 3:
            return 'Aadhaar Card'

        return 'Unknown'
    
    def extract_structured_data(self, text, doc_type):
        """Extract structured data using improved extraction methods"""
        structured_data = {}

        # Use improved extraction methods first (they are more accurate)
        if doc_type == 'PAN Card':
            structured_data = self.extract_pan_data(text)
        elif doc_type == 'Aadhaar Card':
            structured_data = self.extract_aadhaar_data(text)
        elif doc_type == 'Aadhaar XML':
            structured_data = self.extract_aadhaar_xml_data(text)
        elif doc_type == 'Driving License':
            structured_data = self.extract_dl_data(text)
        elif doc_type == 'Passport':
            structured_data = self.extract_passport_data(text)
        elif doc_type == 'Voter ID':
            structured_data = self.extract_voter_data(text)
        # Company documents
        elif doc_type == 'Company PAN Card':
            structured_data = self.extract_company_pan_data(text)
        elif doc_type == 'GST Certificate':
            structured_data = self.extract_gst_data(text)
        elif doc_type == 'Cancelled Cheque':
            structured_data = self.extract_cheque_data(text)
        elif doc_type == 'Registration Certificate':
            structured_data = self.extract_registration_data(text)
        elif doc_type == 'Partnership Deed':
            structured_data = self.extract_partnership_data(text)
        elif doc_type == 'Memorandum Of Association':
            structured_data = self.extract_moa_data(text)
        elif doc_type == 'Article Of Association':
            structured_data = self.extract_aoa_data(text)
        elif doc_type == 'Board Resolution':
            structured_data = self.extract_board_resolution_data(text)
        elif doc_type == 'CIN Certificate':
            structured_data = self.extract_cin_data(text)
        elif doc_type == 'Trust Deed':
            structured_data = self.extract_trust_data(text)
        elif doc_type == 'Society Registration Certificate':
            structured_data = self.extract_society_registration_data(text)
        elif doc_type == 'Society Resolution':
            structured_data = self.extract_society_resolution_data(text)

        # If improved methods didn't work, fallback to document type manager
        if not structured_data and doc_type != 'Unknown':
            structured_data = self.doc_type_manager.extract_structured_data(text, doc_type)

        return structured_data
    
    def extract_pan_data(self, text):
        """Extract PAN card specific data with improved accuracy and quality assessment"""
        data = {}
        quality_issues = []

        # Assess text quality
        text_quality = self._calculate_text_quality(text)
        is_poor_quality = text_quality < 0.7 or len(text.strip()) < 100

        if is_poor_quality:
            quality_issues.append("Poor image quality detected")

        # Clean the text
        lines = [line.strip() for line in text.split('\n') if line.strip()]

        # PAN Number - more robust pattern
        pan_patterns = [
            r'([A-Z]{5}[0-9]{4}[A-Z])',  # Standard PAN format
            r'PAN[:\s]*([A-Z]{5}[0-9]{4}[A-Z])',  # With PAN prefix
        ]
        for pattern in pan_patterns:
            pan_match = re.search(pattern, text)
            if pan_match:
                data['PAN Number'] = pan_match.group(1) if 'PAN' not in pattern else pan_match.group(1)
                break

        # Name extraction - improved for PAN cards
        name_candidates = []

        # Look for names in specific patterns - English only
        # Strategy: Find the person's name (not father's name)

        # Pattern 1: Explicit "Name" label - handle various formats
        name_patterns = [
            r'/\s*Name\s*\n([A-Z][A-Z\s]+?)(?:\s*[\'"]|$)',                # Slash + Name + newline (most common)
            r'(?:^|\n)\s*Name[:\s]*\n?\s*([A-Z][A-Z\s]+?)(?:\s*[/\n]|$)',  # Name on next line
            r'(?:^|\n)\s*Name[:\s]*([A-Z][A-Z\s]+?)(?:\s*[/\n]|Father)',   # Name on same line
            r'/\s*Name[:\s]*\n?\s*([A-Z][A-Z\s]+?)(?:\s*[/\n]|$)',        # With slash prefix
        ]

        for pattern in name_patterns:
            name_match = re.search(pattern, text, re.MULTILINE | re.IGNORECASE)
            if name_match:
                name = name_match.group(1).strip()
                # Clean up any trailing characters or symbols
                name = re.sub(r'[^\w\s].*$', '', name).strip()
                name = re.sub(r'\s+', ' ', name)
                if self._is_valid_pan_name(name):
                    name_candidates.append(name)
                    break

        # Pattern 2: Find standalone name lines that appear AFTER PAN number but BEFORE Father's section
        pan_number_found = False
        father_section_found = False

        for i, line in enumerate(lines):
            line = line.strip()

            # Track when we find PAN number
            if re.match(r'^[A-Z]{5}[0-9]{4}[A-Z]$', line):
                pan_number_found = True
                continue

            # Track when we reach Father's section
            if 'father' in line.lower():
                father_section_found = True
                break

            # Look for name lines after PAN but before Father's section
            if pan_number_found and not father_section_found:
                if (re.match(r'^[A-Z][A-Z\s]+$', line) and
                    2 <= len(line.split()) <= 4 and
                    len(line) >= 10 and
                    self._is_valid_pan_name(line)):
                    name_candidates.append(line)
                    break  # Take the first valid name after PAN number

        if name_candidates:
            # Prefer shorter, cleaner names
            best_name = min(name_candidates, key=len)
            data['Name'] = best_name

        # Father's Name - improved patterns based on actual text structure
        father_patterns = [
            r'/\s*Father[\'s]*\s*Name[^A-Z]*\n([A-Z][A-Z\s]+)',  # Simplified: slash + Father's Name + anything + newline + name
            r'Father[\'s]*\s*Name[^A-Z]*\n\s*([A-Z][A-Z\s]+)',  # Name on next line
            r'/\s*Father[\'s]*\s*Name\s*[-\s]*\s*\d*\s*\n([A-Z][A-Z\s]+?)(?:\s*[a-z]|$)',  # Original pattern
            r'(?:Father[\'s]*\s*Name)\s*[-\s]*\s*\d*[:\s]*([A-Z][A-Z\s]+?)(?:\s*[/\n]|$)',  # Name on same line
        ]

        for pattern in father_patterns:
            father_match = re.search(pattern, text, re.IGNORECASE | re.MULTILINE)
            if father_match:
                father_name = father_match.group(1).strip()
                # Clean up any trailing characters, numbers, or symbols BEFORE validation
                father_name = re.sub(r'\s*[a-z].*$', '', father_name).strip()  # Remove trailing lowercase text
                father_name = re.sub(r'\s*\d+.*$', '', father_name).strip()  # Remove trailing numbers
                father_name = re.sub(r'[^\w\s].*$', '', father_name).strip()  # Remove trailing special chars
                father_name = re.sub(r'\s+', ' ', father_name)

                # Only validate after cleaning
                if father_name and self._is_valid_pan_name(father_name):
                    data['Father\'s Name'] = father_name
                    break

        # Date of Birth - improved patterns
        dob_patterns = [
            r'(?:Date\s*of\s*Birth|DOB)[:\s]*r?\s*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',  # With label
            r'/\s*Date\s*of\s*Birth[:\s]*r?\s*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',     # With slash prefix
            r'(\d{1,2}[/-]\d{1,2}[/-]\d{4})',                                      # Standalone date
            r'(\d{1,2}\.\d{1,2}\.\d{4})',                                          # Dot separated
        ]
        for pattern in dob_patterns:
            dob_match = re.search(pattern, text, re.IGNORECASE)
            if dob_match:
                date_str = dob_match.group(1)
                # Validate the date format
                if self._is_valid_date(date_str):
                    data['Date of Birth'] = date_str
                    break

        # Add quality remarks if there are issues
        if quality_issues:
            data['Quality_Remarks'] = quality_issues

        # Add additional quality checks
        if len(data) <= 1:  # Only PAN number or no data
            if 'Quality_Remarks' not in data:
                data['Quality_Remarks'] = []
            data['Quality_Remarks'].append("Limited data extracted - poor image quality")

        return data

    def _is_valid_pan_name(self, name):
        """Check if a string looks like a valid PAN card name"""
        if not name or len(name) < 3:
            return False

        # PAN names are usually all caps
        if not name.isupper():
            return False

        words = name.split()
        if len(words) < 2 or len(words) > 5:
            return False

        # Check for invalid content
        invalid_words = ['INCOME', 'TAX', 'DEPARTMENT', 'GOVT', 'INDIA', 'PERMANENT', 'ACCOUNT', 'NUMBER']
        if any(word in invalid_words for word in words):
            return False

        # Check for numbers or special characters
        if re.search(r'[0-9]', name) or re.search(r'[^\w\s]', name):
            return False

        return True

    def _is_valid_date(self, date_str):
        """Check if a string looks like a valid date"""
        if not date_str:
            return False

        # Basic format check
        import re
        if not re.match(r'\d{1,2}[/-]\d{1,2}[/-]\d{4}', date_str):
            return False

        # Extract day, month, year
        parts = re.split(r'[/-]', date_str)
        if len(parts) != 3:
            return False

        try:
            day, month, year = map(int, parts)

            # Basic range checks
            if not (1 <= day <= 31):
                return False
            if not (1 <= month <= 12):
                return False
            if not (1900 <= year <= 2030):
                return False

            return True
        except ValueError:
            return False
    
    def extract_aadhaar_data(self, text):
        """Extract Aadhaar card specific data with improved pattern matching and quality assessment"""
        data = {}
        quality_issues = []

        # Assess text quality
        text_quality = self._calculate_text_quality(text)
        is_poor_quality = text_quality < 0.6 or len(text.strip()) < 100

        if is_poor_quality:
            quality_issues.append("Poor image quality detected")

        # Clean the text for better processing
        lines = [line.strip() for line in text.split('\n') if line.strip()]

        # Aadhaar Number - multiple patterns with enhanced detection
        aadhaar_patterns = [
            r'(\d{4}\s+\d{4}\s+\d{4})',  # Standard format with spaces
            r'(\d{4}\s*\d{4}\s*\d{4})',  # With or without spaces
            r'(\d{12})',                  # 12 digits together
            r'(\d{1,4}\s*\d{1,4}\s*\d{1,4})',  # Partial numbers for poor quality
        ]

        aadhaar_found = False
        for pattern in aadhaar_patterns:
            aadhaar_matches = re.findall(pattern, text)
            for match in aadhaar_matches:
                # Clean the match
                clean_number = re.sub(r'\s+', '', match)
                if len(clean_number) >= 10:  # At least 10 digits for partial recognition
                    if len(clean_number) == 12:
                        # Full Aadhaar number
                        formatted_number = f"{clean_number[:4]} {clean_number[4:8]} {clean_number[8:]}"
                        data['Aadhaar Number'] = formatted_number
                        aadhaar_found = True
                        break
                    elif len(clean_number) >= 10:
                        # Partial Aadhaar number
                        data['Aadhaar Number'] = match
                        quality_issues.append("Partial Aadhaar number extracted")
                        aadhaar_found = True
                        break
            if aadhaar_found:
                break
                break

        # Name extraction - enhanced for poor quality OCR
        name_candidates = []

        # Enhanced name extraction for corrupted text
        name_candidates.extend(self._extract_names_from_corrupted_text(text))

        # Look for names in specific patterns - English only
        name_patterns = [
            # Pattern 1: After Name: keyword
            r'(?:name)[:\s]*([A-Z][a-z]+(?:\s+[A-Z][a-z]+){1,3})',
            # Pattern 2: Standalone name lines (2-4 words, proper case)
            r'^([A-Z][a-z]+(?:\s+[A-Z][a-z]+){1,3})$',
            # Pattern 3: After government headers, look for next proper name
            r'(?:government of india|aadhaar).*?\n(?:[^\n]*\n)*?\s*([A-Z][a-z]+(?:\s+[A-Z][a-z]+){1,3})\s*\n',
        ]

        for pattern in name_patterns:
            matches = re.findall(pattern, text, re.MULTILINE | re.IGNORECASE)
            for match in matches:
                # Clean and validate the name
                clean_name = re.sub(r'[^\w\s]', '', match).strip()
                if self._is_valid_name(clean_name):
                    name_candidates.append(clean_name)

        # Also check individual lines for names
        for i, line in enumerate(lines):
            line = line.strip()

            # Skip lines with common non-name content
            skip_words = ['government', 'india', 'aadhaar', 'authority', 'dob', 'address', 'male', 'female', 'year', 'unique', 'identification']
            if any(skip_word in line.lower() for skip_word in skip_words):
                continue

            # Skip lines that are too short or contain numbers/special chars
            if len(line) < 5 or re.search(r'[0-9:/]', line):
                continue

            # Check if line looks like a name (2-4 words, proper case)
            if re.match(r'^[A-Z][a-z]+(?:\s+[A-Z][a-z]+){1,3}$', line):
                if self._is_valid_name(line):
                    # Give higher priority to names that appear after headers
                    if i > 0 and any(header in lines[i-1].lower() for header in ['aadhaar', 'आधार']):
                        name_candidates.insert(0, line)  # Add to front
                    else:
                        name_candidates.append(line)

        # Select the best name candidate with improved logic
        if name_candidates:
            # Score candidates based on multiple factors
            scored_candidates = []

            for candidate in name_candidates:
                words = candidate.split()
                score = 0

                # Prefer 3-word names (typical for Indian names)
                if len(words) == 3:
                    score += 10
                elif len(words) == 2:
                    score += 5
                elif len(words) == 4:
                    score += 3

                # Prefer longer names (more complete)
                score += len(candidate) * 0.1

                # Penalize very short words (likely corruption)
                if any(len(word) <= 2 for word in words):
                    score -= 5

                # Bonus for names that contain common name patterns
                if any(word.lower() in ['ashwamegh', 'gajanan', 'gangasagar'] for word in words):
                    score += 15

                # Penalize names with obvious corruption indicators
                corruption_indicators = ['de', 'fo', 'ee', 'ae', 'oe', 'wt', 'fa', 'soh', 'ok', 'os']
                if any(word.lower() in corruption_indicators for word in words):
                    score -= 10

                # Clean up obvious corruption prefixes/suffixes
                cleaned_words = []
                for word in words:
                    # Skip obvious corruption words at the beginning
                    if (word.lower() in ['soh', 'ok', 'os', 'ea'] and
                        len(words) > 3 and words.index(word) == 0):
                        continue
                    cleaned_words.append(word)

                # Update candidate if we cleaned it
                if len(cleaned_words) != len(words) and len(cleaned_words) >= 2:
                    candidate = ' '.join(cleaned_words)
                    score += 5  # Bonus for cleaning

                scored_candidates.append((candidate, score))

            # Select the highest scoring candidate
            best_candidate = max(scored_candidates, key=lambda x: x[1])
            best_name = best_candidate[0]

            # Final cleanup of the selected name
            best_name = self._clean_extracted_name(best_name)
            data['Name'] = best_name

        # Date of Birth - multiple formats, English only
        dob_patterns = [
            r'(?:DOB|Birth)[:\s]*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            r'(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            r'(\d{1,2}\.\d{1,2}\.\d{4})',
        ]
        for pattern in dob_patterns:
            dob_match = re.search(pattern, text, re.IGNORECASE)
            if dob_match:
                data['Date of Birth'] = dob_match.group(1)
                break

        # Gender - English only
        gender_patterns = [
            r'\b(male|female)\b',
            r'(?:gender)[:\s]*(male|female)',
        ]
        for pattern in gender_patterns:
            gender_match = re.search(pattern, text, re.IGNORECASE)
            if gender_match:
                gender = gender_match.group(1).lower()
                if gender == 'male':
                    data['Gender'] = 'Male'
                elif gender == 'female':
                    data['Gender'] = 'Female'
                break

        # Address - improved extraction
        address_patterns = [
            r'(?:Address|पता)[:\s]*(.+?)(?:\n\n|\n[A-Z]|\d{4}\s*\d{4})',
            r'(?:S/O|D/O|W/O)[:\s]*[^,\n]+,\s*(.+?)(?:\n\n|\n[A-Z])',
        ]
        for pattern in address_patterns:
            address_match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if address_match:
                address = address_match.group(1).strip()
                # Clean up the address
                address = re.sub(r'\s+', ' ', address)
                if len(address) > 10:  # Only if we got a reasonable address
                    data['Address'] = address
                    break

        # Add quality remarks if there are issues
        if quality_issues:
            data['Quality_Remarks'] = quality_issues

        # Add additional quality checks
        if len(data) <= 2:  # Only Aadhaar number and maybe one other field
            if 'Quality_Remarks' not in data:
                data['Quality_Remarks'] = []
            data['Quality_Remarks'].append("Limited data extracted - poor image quality")

        return data

    def _is_valid_name(self, name):
        """Check if a string looks like a valid person name"""
        if not name or len(name) < 3:
            return False

        words = name.split()
        if len(words) < 2 or len(words) > 4:
            return False

        # Check if all words start with capital letter
        if not all(word[0].isupper() for word in words):
            return False

        # Check for common non-name patterns
        invalid_patterns = [
            r'\d',  # Contains numbers
            r'[^\w\s]',  # Contains special characters
        ]

        for pattern in invalid_patterns:
            if re.search(pattern, name):
                return False

        # Check for invalid words that shouldn't be in names
        invalid_words = ['GOVERNMENT', 'INDIA', 'AADHAAR', 'AUTHORITY', 'UNIQUE', 'IDENTIFICATION', 'CARD', 'DOB', 'ADDRESS']
        name_upper = name.upper()
        if any(word in name_upper for word in invalid_words):
            return False

        # Check if words are reasonable length (not too short or too long)
        if any(len(word) < 2 or len(word) > 15 for word in words):
            return False

        return True

    def _extract_names_from_corrupted_text(self, text):
        """Extract names from corrupted OCR text using advanced pattern matching"""
        name_candidates = []

        # Split text into lines for analysis
        lines = text.split('\n')

        # Look for patterns like "Ashwamegh'Gajanan ee\nDe fo i, Gangasagar"
        # This handles names split across lines with corruption
        for i, line in enumerate(lines):
            line = line.strip()

            # Look for lines that might contain name parts
            # Pattern: capital letter followed by lowercase, possibly with corruption
            potential_name_parts = re.findall(r'([A-Z][a-z]+(?:\'[A-Z][a-z]+)?)', line)

            if potential_name_parts:
                # Try to reconstruct the full name from this and next lines
                full_name_parts = []
                full_name_parts.extend(potential_name_parts)

                # Check next few lines for continuation
                for j in range(i + 1, min(i + 3, len(lines))):
                    next_line = lines[j].strip()
                    # Look for more name parts in subsequent lines
                    more_parts = re.findall(r'([A-Z][a-z]+)', next_line)

                    # Only add if they look like name parts (not common words)
                    for part in more_parts:
                        if (len(part) >= 3 and
                            part.lower() not in ['male', 'female', 'india', 'govt', 'dob', 'address'] and
                            not re.search(r'\d', part)):
                            full_name_parts.append(part)

                # Try to form a complete name
                if len(full_name_parts) >= 2:
                    # Clean up the parts
                    cleaned_parts = []
                    for part in full_name_parts:
                        # Remove apostrophes and clean
                        clean_part = re.sub(r'[^\w]', '', part)
                        if len(clean_part) >= 2:
                            cleaned_parts.append(clean_part.title())

                    if len(cleaned_parts) >= 2:
                        candidate_name = ' '.join(cleaned_parts[:4])  # Max 4 parts

                        # Validate the candidate
                        if self._is_valid_aadhaar_name_candidate(candidate_name):
                            name_candidates.append(candidate_name)

        # Also try to extract from specific corrupted patterns
        # Pattern like "Ashwamegh'Gajanan ee\nDe fo i, Gangasagar"
        corrupted_patterns = [
            r'([A-Z][a-z]+)\'([A-Z][a-z]+)\s+[a-z]+\s*\n[^A-Z]*([A-Z][a-z]+)',
            r'([A-Z][a-z]+)\s+([A-Z][a-z]+)\s+[a-z]+\s*\n[^A-Z]*([A-Z][a-z]+)',
        ]

        for pattern in corrupted_patterns:
            matches = re.findall(pattern, text, re.MULTILINE)
            for match in matches:
                # Reconstruct the name from the captured groups
                name_parts = [part for part in match if part and len(part) >= 2]
                if len(name_parts) >= 2:
                    candidate_name = ' '.join(name_parts)
                    if self._is_valid_aadhaar_name_candidate(candidate_name):
                        name_candidates.append(candidate_name)

        # Special handling for the specific ash-adhar pattern
        # "Ashwamegh'Gajanan ee\nDe fo i, Gangasagar"
        special_match = re.search(r'([A-Z][a-z]+)\'([A-Z][a-z]+)[^A-Z]*\n[^A-Z]*([A-Z][a-z]+)', text)
        if special_match:
            # This should capture: Ashwamegh, Gajanan, Gangasagar
            first_name = special_match.group(1)
            middle_name = special_match.group(2)
            last_name = special_match.group(3)

            # Only use if last_name looks like a real surname (not corruption)
            if (len(last_name) >= 4 and
                last_name.lower() not in ['male', 'female', 'india', 'govt'] and
                not re.search(r'\d', last_name)):

                full_name = f"{first_name} {middle_name} {last_name}"
                if self._is_valid_aadhaar_name_candidate(full_name):
                    name_candidates.append(full_name)

        return name_candidates

    def _is_valid_aadhaar_name_candidate(self, name):
        """Check if a name candidate is valid for Aadhaar cards"""
        if not name or len(name) < 5:
            return False

        words = name.split()
        if len(words) < 2 or len(words) > 5:
            return False

        # Check if all words are reasonable length
        if not all(2 <= len(word) <= 15 for word in words):
            return False

        # Check for invalid patterns
        invalid_words = ['government', 'india', 'aadhaar', 'authority', 'unique', 'identification',
                        'male', 'female', 'address', 'birth', 'date']
        if any(word.lower() in invalid_words for word in words):
            return False

        # Check for numbers or excessive special characters
        if re.search(r'\d', name) or len(re.findall(r'[^\w\s]', name)) > 1:
            return False

        return True

    def _clean_extracted_name(self, name):
        """Clean up extracted name by removing obvious corruption"""
        if not name:
            return name

        words = name.split()
        cleaned_words = []

        for i, word in enumerate(words):
            # Remove obvious corruption words
            corruption_words = ['soh', 'ok', 'os', 'ea', 'de', 'fo', 'ae', 'oe']

            # Skip corruption words, especially at the beginning
            if word.lower() in corruption_words:
                # Only skip if we have enough other words
                if len(words) > 3 or (len(words) > 2 and i == 0):
                    continue

            # Skip very short words (likely corruption) unless it's a valid short name
            if len(word) <= 2 and word.lower() not in ['jo', 'li', 'su']:
                if len(words) > 3:  # Only skip if we have enough other words
                    continue

            cleaned_words.append(word)

        # Ensure we have at least 2 words
        if len(cleaned_words) >= 2:
            return ' '.join(cleaned_words)
        else:
            return name  # Return original if cleaning would make it too short

    def _detect_and_correct_orientation(self, img):
        """Detect and correct image orientation using OCR confidence scores"""
        if img is None:
            return img

        # Skip if already processed (to avoid multiple calls)
        if hasattr(self, '_orientation_checked'):
            return img
        self._orientation_checked = True

        print("🔄 Detecting image orientation...")

        # Test different orientations (0°, 90°, 180°, 270°)
        orientations = [
            (0, img),  # Original
            (90, cv2.rotate(img, cv2.ROTATE_90_CLOCKWISE)),
            (180, cv2.rotate(img, cv2.ROTATE_180)),
            (270, cv2.rotate(img, cv2.ROTATE_90_COUNTERCLOCKWISE))
        ]

        best_orientation = 0
        best_score = -1
        best_img = img

        for angle, rotated_img in orientations:
            try:
                # Quick OCR test to determine orientation quality
                gray = cv2.cvtColor(rotated_img, cv2.COLOR_BGR2GRAY)

                # Use Tesseract to get confidence score
                from PIL import Image
                import pytesseract

                pil_img = Image.fromarray(gray)

                # Get OCR data with confidence scores - use simpler config for speed
                ocr_data = pytesseract.image_to_data(pil_img, config='--psm 6', output_type=pytesseract.Output.DICT)

                # Calculate average confidence for text detection
                confidences = [int(conf) for conf in ocr_data['conf'] if int(conf) > 0]

                if confidences:
                    avg_confidence = sum(confidences) / len(confidences)
                    text_length = len(''.join(ocr_data['text']).strip())

                    # Score based on confidence and text length
                    score = avg_confidence * (1 + text_length / 100)

                    print(f"   {angle:3d}°: confidence={avg_confidence:.1f}, text_length={text_length:3d}, score={score:.1f}")

                    if score > best_score:
                        best_score = score
                        best_orientation = angle
                        best_img = rotated_img

                        # Early exit if we find very good orientation
                        if avg_confidence > 75 and text_length > 100:
                            print(f"   ✅ Found excellent orientation at {angle}°, stopping search")
                            break

            except Exception as e:
                print(f"   ❌ Error testing {angle}°: {e}")
                continue

        if best_orientation != 0:
            print(f"✅ Corrected orientation: rotated {best_orientation}° (score: {best_score:.1f})")
        else:
            print(f"✅ Original orientation is best (score: {best_score:.1f})")

        return best_img

    def extract_dl_data(self, text):
        """Extract Driving License specific data"""
        data = {}

        # License Number
        dl_patterns = [
            r'DL[:\s]*([A-Z0-9]+)',
            r'License[:\s]*([A-Z0-9]+)',
        ]
        for pattern in dl_patterns:
            dl_match = re.search(pattern, text, re.IGNORECASE)
            if dl_match:
                data['License Number'] = dl_match.group(1)
                break

        # Name
        name_patterns = [
            r'Name[:\s]*([A-Z\s]+?)(?:\n|$)',
            r'^([A-Z][a-z]+(?:\s[A-Z][a-z]+)+)',
        ]
        for pattern in name_patterns:
            name_match = re.search(pattern, text, re.MULTILINE | re.IGNORECASE)
            if name_match:
                data['Name'] = name_match.group(1).strip()
                break

        return data

    def extract_passport_data(self, text):
        """Extract Passport specific data"""
        data = {}

        # Passport Number
        passport_match = re.search(r'([A-Z]\d{7})', text)
        if passport_match:
            data['Passport Number'] = passport_match.group(1)

        return data

    def extract_voter_data(self, text):
        """Extract Voter ID specific data"""
        data = {}

        # Voter ID Number
        voter_patterns = [
            r'([A-Z]{3}\d{7})',
            r'EPIC[:\s]*([A-Z0-9]+)',
        ]
        for pattern in voter_patterns:
            voter_match = re.search(pattern, text)
            if voter_match:
                data['Voter ID'] = voter_match.group(1)
                break

        return data

    def process_document(self, image_path, filename):
        """Main method to process a document"""
        # Extract text
        raw_text = self.extract_text_from_image(image_path)

        # Identify document type
        doc_type = self.identify_document_type(raw_text)

        # Create base result
        result = {
            'filename': filename,
            'Document Type': doc_type,
            'Raw Text': raw_text
        }

        # If document type is unknown, provide suggestions
        if doc_type == 'Unknown':
            suggestions = self.get_document_suggestions(raw_text)
            result['Suggestions'] = suggestions
            result['Manual_Classification_Needed'] = True
        else:
            # Extract structured data if document type is recognized
            structured_data = self.extract_structured_data(raw_text, doc_type)
            result.update(structured_data)

            # Learn from this document for future improvements
            self.doc_type_manager.learn_from_document(filename, doc_type, structured_data, raw_text)

        return result

    def get_document_suggestions(self, text):
        """Provide suggestions for unknown documents"""
        suggestions = {
            'possible_types': [],
            'detected_patterns': [],
            'keywords_found': [],
            'manual_hints': []
        }

        # Check for partial matches
        text_lower = text.lower()

        # Check each document type for partial matches
        for doc_type, config in self.doc_type_manager.document_types.items():
            keyword_matches = []
            pattern_matches = []

            # Check keywords
            for keyword in config['keywords']:
                if keyword in text_lower:
                    keyword_matches.append(keyword)

            # Check patterns
            for field, pattern in config['patterns'].items():
                if re.search(pattern, text, re.IGNORECASE):
                    pattern_matches.append(field)

            # If we have some matches, suggest this type
            if keyword_matches or pattern_matches:
                suggestions['possible_types'].append({
                    'type': doc_type,
                    'keywords_found': keyword_matches,
                    'patterns_found': pattern_matches,
                    'confidence': len(keyword_matches) + len(pattern_matches)
                })

        # Sort by confidence
        suggestions['possible_types'].sort(key=lambda x: x['confidence'], reverse=True)

        # Detect common patterns
        if re.search(r'[A-Z]{5}[0-9]{4}[A-Z]', text):
            suggestions['detected_patterns'].append('PAN Number format detected')

        if re.search(r'\d{4}\s*\d{4}\s*\d{4}', text):
            suggestions['detected_patterns'].append('12-digit number (possibly Aadhaar) detected')

        if re.search(r'[A-Z]{2}[0-9]{2}[A-Z0-9]+', text):
            suggestions['detected_patterns'].append('License number format detected')

        # Provide manual hints
        suggestions['manual_hints'] = [
            "Look for government logos or headers",
            "Check for specific number formats (PAN: **********, Aadhaar: 1234 5678 9012)",
            "Look for keywords like 'Income Tax', 'Aadhaar', 'Driving License'",
            "Check document layout and structure"
        ]

        return suggestions

    # INDIVIDUAL DOCUMENT EXTRACTION METHODS
    def extract_aadhaar_xml_data(self, text):
        """Extract Aadhaar XML specific data"""
        data = {}

        # XML-specific patterns
        xml_patterns = {
            'Aadhaar Number': r'(\d{4}\s*\d{4}\s*\d{4})',
            'Name': r'(?:name)[:\s]*([A-Za-z\s]+?)(?:\n|$)',
            'Date of Birth': r'(?:dob|birth)[:\s]*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            'Gender': r'\b(male|female|M|F)\b',
            'Address': r'(?:address)[:\s]*(.+?)(?:\n\n|\n[A-Z])',
        }

        for field, pattern in xml_patterns.items():
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                data[field] = match.group(1).strip()

        return data

    # COMPANY DOCUMENT EXTRACTION METHODS
    def extract_company_pan_data(self, text):
        """Extract Company PAN card specific data"""
        data = {}

        # Company PAN patterns
        patterns = {
            'PAN Number': r'([A-Z]{5}[0-9]{4}[A-Z])',
            'Company Name': r'(?:Name)[:\s]*([A-Za-z\s&.,()]+?)(?:\n|$)',
            'Registration Date': r'(?:Date)[:\s]*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            'Status': r'(?:Status)[:\s]*([A-Za-z\s]+)'
        }

        for field, pattern in patterns.items():
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                data[field] = match.group(1).strip()

        return data

    def extract_gst_data(self, text):
        """Extract GST Certificate specific data"""
        data = {}

        patterns = {
            'GSTIN': r'(\d{2}[A-Z]{5}\d{4}[A-Z]\d[Z][A-Z\d])',
            'Company Name': r'(?:Name|Legal Name)[:\s]*([A-Za-z\s&.,()]+?)(?:\n|$)',
            'Registration Date': r'(?:Date)[:\s]*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            'State': r'(?:State)[:\s]*([A-Za-z\s]+)',
            'Address': r'(?:Address)[:\s]*(.+?)(?:\n\n|\n[A-Z])'
        }

        for field, pattern in patterns.items():
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                data[field] = match.group(1).strip()

        return data

    def extract_cheque_data(self, text):
        """Extract Cancelled Cheque specific data"""
        data = {}

        patterns = {
            'Account Number': r'(\d{9,18})',
            'IFSC Code': r'([A-Z]{4}0[A-Z0-9]{6})',
            'MICR Code': r'(\d{9})',
            'Bank Name': r'(?:Bank)[:\s]*([A-Za-z\s&.,()]+?)(?:\n|$)',
            'Account Holder': r'(?:Name|Account Holder)[:\s]*([A-Za-z\s&.,()]+?)(?:\n|$)'
        }

        for field, pattern in patterns.items():
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                data[field] = match.group(1).strip()

        return data

    def extract_registration_data(self, text):
        """Extract Registration Certificate specific data"""
        data = {}

        patterns = {
            'Registration Number': r'([A-Z0-9\-]+)',
            'Company Name': r'(?:Name)[:\s]*([A-Za-z\s&.,()]+?)(?:\n|$)',
            'Registration Date': r'(?:Date)[:\s]*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            'State': r'(?:State)[:\s]*([A-Za-z\s]+)',
            'Type': r'(?:Type|Category)[:\s]*([A-Za-z\s]+)'
        }

        for field, pattern in patterns.items():
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                data[field] = match.group(1).strip()

        return data

    def extract_partnership_data(self, text):
        """Extract Partnership Deed specific data"""
        data = {}

        patterns = {
            'Firm Name': r'(?:Firm|Partnership)[:\s]*([A-Za-z\s&.,()]+?)(?:\n|$)',
            'Partners': r'(?:Partners)[:\s]*([A-Za-z\s,&.()]+)',
            'Date': r'(?:Date)[:\s]*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            'Registration Number': r'([A-Z0-9\-]+)',
            'Address': r'(?:Address)[:\s]*(.+?)(?:\n\n|\n[A-Z])'
        }

        for field, pattern in patterns.items():
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                data[field] = match.group(1).strip()

        return data

    def extract_moa_data(self, text):
        """Extract Memorandum Of Association specific data"""
        data = {}

        patterns = {
            'Company Name': r'(?:Name)[:\s]*([A-Za-z\s&.,()]+?)(?:\n|$)',
            'Registration Number': r'([A-Z0-9\-]+)',
            'Date': r'(?:Date)[:\s]*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            'State': r'(?:State)[:\s]*([A-Za-z\s]+)',
            'Objects': r'(?:Objects)[:\s]*(.+?)(?:\n\n|\n[A-Z])'
        }

        for field, pattern in patterns.items():
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                data[field] = match.group(1).strip()

        return data

    def extract_aoa_data(self, text):
        """Extract Article Of Association specific data"""
        data = {}

        patterns = {
            'Company Name': r'(?:Name)[:\s]*([A-Za-z\s&.,()]+?)(?:\n|$)',
            'Registration Number': r'([A-Z0-9\-]+)',
            'Date': r'(?:Date)[:\s]*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            'State': r'(?:State)[:\s]*([A-Za-z\s]+)',
            'Articles': r'(?:Article)[:\s]*(.+?)(?:\n\n|\n[A-Z])'
        }

        for field, pattern in patterns.items():
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                data[field] = match.group(1).strip()

        return data

    def extract_board_resolution_data(self, text):
        """Extract Board Resolution specific data"""
        data = {}

        patterns = {
            'Company Name': r'(?:Name)[:\s]*([A-Za-z\s&.,()]+?)(?:\n|$)',
            'Resolution Number': r'(?:Resolution)[:\s]*([A-Z0-9\-]+)',
            'Date': r'(?:Date)[:\s]*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            'Directors': r'(?:Directors)[:\s]*([A-Za-z\s,&.()]+)',
            'Resolution': r'(?:Resolved)[:\s]*(.+?)(?:\n\n|\n[A-Z])'
        }

        for field, pattern in patterns.items():
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                data[field] = match.group(1).strip()

        return data

    def extract_cin_data(self, text):
        """Extract CIN Certificate specific data"""
        data = {}

        patterns = {
            'CIN': r'([LU]\d{5}[A-Z]{2}\d{4}[A-Z]{3}\d{6})',
            'Company Name': r'(?:Name)[:\s]*([A-Za-z\s&.,()]+?)(?:\n|$)',
            'Registration Date': r'(?:Date)[:\s]*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            'State': r'(?:State)[:\s]*([A-Za-z\s]+)',
            'Status': r'(?:Status)[:\s]*([A-Za-z\s]+)'
        }

        for field, pattern in patterns.items():
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                data[field] = match.group(1).strip()

        return data

    def extract_trust_data(self, text):
        """Extract Trust Deed specific data"""
        data = {}

        patterns = {
            'Trust Name': r'(?:Trust|Name)[:\s]*([A-Za-z\s&.,()]+?)(?:\n|$)',
            'Registration Number': r'([A-Z0-9\-]+)',
            'Date': r'(?:Date)[:\s]*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            'Trustees': r'(?:Trustee)[:\s]*([A-Za-z\s,&.()]+)',
            'Purpose': r'(?:Purpose|Object)[:\s]*(.+?)(?:\n\n|\n[A-Z])'
        }

        for field, pattern in patterns.items():
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                data[field] = match.group(1).strip()

        return data

    def extract_society_registration_data(self, text):
        """Extract Society Registration Certificate specific data"""
        data = {}

        patterns = {
            'Society Name': r'(?:Society|Name)[:\s]*([A-Za-z\s&.,()]+?)(?:\n|$)',
            'Registration Number': r'([A-Z0-9\-]+)',
            'Registration Date': r'(?:Date)[:\s]*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            'State': r'(?:State)[:\s]*([A-Za-z\s]+)',
            'Address': r'(?:Address)[:\s]*(.+?)(?:\n\n|\n[A-Z])'
        }

        for field, pattern in patterns.items():
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                data[field] = match.group(1).strip()

        return data

    def extract_society_resolution_data(self, text):
        """Extract Society Resolution specific data"""
        data = {}

        patterns = {
            'Society Name': r'(?:Society|Name)[:\s]*([A-Za-z\s&.,()]+?)(?:\n|$)',
            'Resolution Number': r'(?:Resolution)[:\s]*([A-Z0-9\-]+)',
            'Date': r'(?:Date)[:\s]*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            'Members': r'(?:Members)[:\s]*([A-Za-z\s,&.()]+)',
            'Resolution': r'(?:Resolved)[:\s]*(.+?)(?:\n\n|\n[A-Z])'
        }

        for field, pattern in patterns.items():
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                data[field] = match.group(1).strip()

        return data

    # ADVANCED PREPROCESSING METHODS FOR LOW QUALITY AND SKEWED IMAGES

    def _skew_correction_preprocessing(self, img):
        """Advanced skew correction for tilted/rotated documents"""
        import numpy as np

        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # Detect skew angle using Hough line detection
        skew_angle = self._detect_skew_angle(gray)

        # Rotate image to correct skew
        if abs(skew_angle) > 0.5:  # Only correct if skew is significant
            corrected = self._rotate_image(gray, skew_angle)
        else:
            corrected = gray

        # Apply standard enhancement after skew correction
        height, width = corrected.shape
        if height < 1000 or width < 1200:
            scale_factor = max(1200/width, 1000/height)
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            corrected = cv2.resize(corrected, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

        # Enhance contrast
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        enhanced = clahe.apply(corrected)

        # Denoise
        denoised = cv2.fastNlMeansDenoising(enhanced, h=10)

        # Sharpen
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(denoised, -1, kernel)

        # Threshold
        _, thresh = cv2.threshold(sharpened, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        return thresh

    def _detect_skew_angle(self, gray):
        """Detect skew angle using Hough line detection"""
        import numpy as np

        # Edge detection
        edges = cv2.Canny(gray, 50, 150, apertureSize=3)

        # Hough line detection
        lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)

        if lines is not None:
            angles = []
            for rho, theta in lines[:20]:  # Use first 20 lines
                angle = np.degrees(theta) - 90
                if abs(angle) < 45:  # Only consider reasonable angles
                    angles.append(angle)

            if angles:
                # Return median angle to avoid outliers
                return np.median(angles)

        return 0

    def _rotate_image(self, image, angle):
        """Rotate image by given angle"""
        import numpy as np

        height, width = image.shape
        center = (width // 2, height // 2)

        # Get rotation matrix
        rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)

        # Calculate new dimensions
        cos_angle = abs(rotation_matrix[0, 0])
        sin_angle = abs(rotation_matrix[0, 1])
        new_width = int((height * sin_angle) + (width * cos_angle))
        new_height = int((height * cos_angle) + (width * sin_angle))

        # Adjust rotation matrix for new center
        rotation_matrix[0, 2] += (new_width / 2) - center[0]
        rotation_matrix[1, 2] += (new_height / 2) - center[1]

        # Rotate image
        rotated = cv2.warpAffine(image, rotation_matrix, (new_width, new_height),
                                flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_REPLICATE)

        return rotated

    def _perspective_correction_preprocessing(self, img):
        """Correct perspective distortion for documents photographed at angles"""
        import numpy as np

        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # Try to detect document corners and apply perspective correction
        corrected = self._detect_and_correct_perspective(gray)

        # If perspective correction failed, use original
        if corrected is None:
            corrected = gray

        # Apply enhancement after perspective correction
        height, width = corrected.shape
        if height < 1200 or width < 1500:
            scale_factor = max(1500/width, 1200/height)
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            corrected = cv2.resize(corrected, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

        # Advanced denoising
        denoised = cv2.bilateralFilter(corrected, 9, 75, 75)

        # Enhance contrast
        clahe = cv2.createCLAHE(clipLimit=4.0, tileGridSize=(8,8))
        enhanced = clahe.apply(denoised)

        # Morphological operations to clean up
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        cleaned = cv2.morphologyEx(enhanced, cv2.MORPH_CLOSE, kernel)

        # Threshold
        _, thresh = cv2.threshold(cleaned, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        return thresh

    def _detect_and_correct_perspective(self, gray):
        """Detect document corners and apply perspective correction"""
        import numpy as np

        try:
            # Edge detection
            edges = cv2.Canny(gray, 50, 150)

            # Find contours
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # Find the largest contour (likely the document)
            if contours:
                largest_contour = max(contours, key=cv2.contourArea)

                # Approximate contour to get corners
                epsilon = 0.02 * cv2.arcLength(largest_contour, True)
                approx = cv2.approxPolyDP(largest_contour, epsilon, True)

                # If we found 4 corners, apply perspective correction
                if len(approx) == 4:
                    # Order the corners
                    corners = self._order_corners(approx.reshape(4, 2))

                    # Calculate destination dimensions
                    width = max(
                        np.linalg.norm(corners[1] - corners[0]),
                        np.linalg.norm(corners[3] - corners[2])
                    )
                    height = max(
                        np.linalg.norm(corners[3] - corners[0]),
                        np.linalg.norm(corners[2] - corners[1])
                    )

                    # Define destination corners
                    dst_corners = np.array([
                        [0, 0],
                        [width - 1, 0],
                        [width - 1, height - 1],
                        [0, height - 1]
                    ], dtype=np.float32)

                    # Get perspective transform matrix
                    matrix = cv2.getPerspectiveTransform(corners.astype(np.float32), dst_corners)

                    # Apply perspective correction
                    corrected = cv2.warpPerspective(gray, matrix, (int(width), int(height)))

                    return corrected
        except Exception as e:
            print(f"Perspective correction failed: {e}")

        return None

    def _order_corners(self, corners):
        """Order corners as top-left, top-right, bottom-right, bottom-left"""
        import numpy as np

        # Sort by y-coordinate
        sorted_corners = corners[np.argsort(corners[:, 1])]

        # Top two points
        top_points = sorted_corners[:2]
        # Bottom two points
        bottom_points = sorted_corners[2:]

        # Sort top points by x-coordinate (left to right)
        top_points = top_points[np.argsort(top_points[:, 0])]
        # Sort bottom points by x-coordinate (right to left for correct order)
        bottom_points = bottom_points[np.argsort(bottom_points[:, 0])[::-1]]

        # Return in order: top-left, top-right, bottom-right, bottom-left
        return np.array([top_points[0], top_points[1], bottom_points[0], bottom_points[1]])

    def _super_enhancement_preprocessing(self, img):
        """Super enhancement for extremely poor quality images"""
        import numpy as np

        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # Extreme upscaling with interpolation
        height, width = gray.shape
        scale_factor = max(4.0, 2500 / max(height, width))
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)

        # Use LANCZOS for better quality upscaling
        upscaled = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)

        # Multiple denoising passes
        denoised1 = cv2.fastNlMeansDenoising(upscaled, h=10)
        denoised2 = cv2.bilateralFilter(denoised1, 9, 75, 75)

        # Adaptive histogram equalization
        clahe = cv2.createCLAHE(clipLimit=5.0, tileGridSize=(4,4))
        enhanced = clahe.apply(denoised2)

        # Unsharp masking for better text clarity
        gaussian = cv2.GaussianBlur(enhanced, (0, 0), 2.0)
        unsharp = cv2.addWeighted(enhanced, 1.5, gaussian, -0.5, 0)

        # Morphological operations to connect broken characters
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 1))
        morph = cv2.morphologyEx(unsharp, cv2.MORPH_CLOSE, kernel)

        # Adaptive thresholding for better text separation
        thresh = cv2.adaptiveThreshold(morph, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                     cv2.THRESH_BINARY, 11, 2)

        return thresh

    def _multi_approach_preprocessing(self, img):
        """Try multiple preprocessing approaches and return the best result"""
        import numpy as np

        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        approaches = []

        # Approach 1: Skew correction + enhancement
        try:
            result1 = self._skew_correction_preprocessing(img)
            approaches.append(('skew_corrected', result1))
        except:
            pass

        # Approach 2: Perspective correction + enhancement
        try:
            result2 = self._perspective_correction_preprocessing(img)
            approaches.append(('perspective_corrected', result2))
        except:
            pass

        # Approach 3: Super enhancement
        try:
            result3 = self._super_enhancement_preprocessing(img)
            approaches.append(('super_enhanced', result3))
        except:
            pass

        # Approach 4: Aggressive preprocessing
        try:
            result4 = self._aggressive_preprocessing(img)
            approaches.append(('aggressive', result4))
        except:
            pass

        # If we have multiple approaches, return the one with best text clarity
        if len(approaches) > 1:
            best_approach = self._select_best_preprocessing(approaches)
            return best_approach[1]
        elif approaches:
            return approaches[0][1]
        else:
            # Fallback to standard preprocessing
            return self._standard_preprocessing(img)

    def _select_best_preprocessing(self, approaches):
        """Select the best preprocessing result based on text clarity metrics"""
        import numpy as np

        best_score = -1
        best_approach = approaches[0]

        for name, processed_img in approaches:
            try:
                # Calculate text clarity score
                score = self._calculate_text_clarity_score(processed_img)

                if score > best_score:
                    best_score = score
                    best_approach = (name, processed_img)
            except:
                continue

        return best_approach

    def _calculate_text_clarity_score(self, img):
        """Calculate a score indicating text clarity in the image"""
        import numpy as np

        # Calculate edge density (more edges = clearer text)
        edges = cv2.Canny(img, 50, 150)
        edge_density = np.sum(edges > 0) / (img.shape[0] * img.shape[1])

        # Calculate contrast (higher contrast = clearer text)
        contrast = img.std()

        # Calculate sharpness using Laplacian variance
        laplacian = cv2.Laplacian(img, cv2.CV_64F)
        sharpness = laplacian.var()

        # Combine metrics (weights can be adjusted)
        score = (edge_density * 1000) + (contrast * 2) + (sharpness * 0.1)

        return score

    def extract_text_with_multiple_configs(self, image_path, preprocessing_method='multi_approach'):
        """Extract text using multiple OCR configurations for challenging images"""

        # Preprocess the image
        processed_img = self.preprocess_image(image_path, preprocessing_method)

        # Save processed image temporarily
        temp_path = image_path.replace('.', '_processed.')
        cv2.imwrite(temp_path, processed_img)

        # Try multiple Tesseract configurations
        configs = [
            '--psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz ',
            '--psm 8 --oem 3',
            '--psm 7 --oem 1',
            '--psm 6 --oem 3 -c tessedit_char_blacklist=|',
            '--psm 4 --oem 3',
            '--psm 3 --oem 3',
            '--psm 1 --oem 3'
        ]

        best_text = ""
        best_confidence = 0

        for config in configs:
            try:
                # Extract text with current config
                data = pytesseract.image_to_data(temp_path, config=config, output_type=pytesseract.Output.DICT)

                # Calculate average confidence
                confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
                if confidences:
                    avg_confidence = sum(confidences) / len(confidences)

                    # Extract text
                    text = pytesseract.image_to_string(temp_path, config=config)

                    # If this configuration gives better results, use it
                    if avg_confidence > best_confidence and len(text.strip()) > len(best_text.strip()):
                        best_confidence = avg_confidence
                        best_text = text

            except Exception as e:
                print(f"OCR config failed: {config}, Error: {e}")
                continue

        # Clean up temporary file
        try:
            import os
            os.remove(temp_path)
        except:
            pass

        # If no good result, fallback to standard extraction
        if not best_text.strip():
            best_text = self.extract_text_from_image(image_path)

        return best_text
