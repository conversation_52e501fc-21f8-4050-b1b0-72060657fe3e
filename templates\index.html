<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document OCR Extraction System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .upload-area {
            border: 2px dashed #007bff;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            background-color: #e9ecef;
            border-color: #0056b3;
        }
        .upload-area.dragover {
            background-color: #cce5ff;
            border-color: #0056b3;
        }
        .file-preview {
            max-width: 100px;
            max-height: 100px;
            object-fit: cover;
            border-radius: 5px;
        }
        .result-card {
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        .document-type-badge {
            font-size: 0.8em;
        }
        .loading {
            display: none;
        }
        .json-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .unknown-document {
            border-left: 4px solid #ffc107;
            background-color: #fff3cd;
        }
        .suggestions-box {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }
        .manual-classify-btn {
            margin: 5px;
        }
        .view-file-btn {
            font-size: 0.875rem;
            padding: 0.25rem 0.5rem;
            transition: all 0.2s ease;
        }
        .view-file-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .gap-2 {
            gap: 0.5rem !important;
        }
        #fileViewModal .modal-body {
            padding: 1.5rem;
        }
        #modalImage {
            border: 1px solid #dee2e6;
            transition: transform 0.2s ease;
        }
        #modalImage:hover {
            transform: scale(1.02);
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-file-text text-primary"></i>
                    Document OCR Extraction System
                </h1>
                <p class="text-center text-muted mb-5">
                    Upload multiple government ID documents for automatic OCR text extraction and data parsing
                </p>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-upload"></i> Upload Documents</h5>
                    </div>
                    <div class="card-body">
                        <form id="uploadForm" enctype="multipart/form-data">
                            <div class="upload-area" id="uploadArea">
                                <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                <h5>Drag & Drop Files Here</h5>
                                <p class="text-muted">or click to browse</p>
                                <input type="file" id="fileInput" name="files" multiple accept="image/*,.pdf" style="display: none;">
                                <button type="button" class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                                    <i class="fas fa-folder-open"></i> Browse Files
                                </button>
                            </div>
                            
                            <div id="fileList" class="mt-3"></div>
                            
                            <div class="mt-3">
                                <button type="submit" class="btn btn-success btn-lg w-100" id="processBtn">
                                    <i class="fas fa-cogs"></i> Process Documents
                                </button>
                            </div>
                        </form>
                        
                        <div class="loading mt-3" id="loadingDiv">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">Processing documents...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> Supported Document Types</h5>
                    </div>
                    <div class="card-body">
                        <!-- Individual Documents -->
                        <div class="mb-3">
                            <h6 class="text-primary"><i class="fas fa-user"></i> Individual Documents (6 types)</h6>
                            <div class="row">
                                <div class="col-6">
                                    <ul class="list-unstyled small">
                                        <li><i class="fas fa-id-card text-success"></i> PAN Card</li>
                                        <li><i class="fas fa-id-badge text-info"></i> Aadhaar Card</li>
                                        <li><i class="fas fa-file-code text-info"></i> Aadhaar XML</li>
                                    </ul>
                                </div>
                                <div class="col-6">
                                    <ul class="list-unstyled small">
                                        <li><i class="fas fa-car text-warning"></i> Driving License</li>
                                        <li><i class="fas fa-passport text-primary"></i> Passport</li>
                                        <li><i class="fas fa-vote-yea text-secondary"></i> Voter ID</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Company Documents -->
                        <div class="mb-3">
                            <h6 class="text-success"><i class="fas fa-building"></i> Company Documents (12 types)</h6>
                            <div class="row">
                                <div class="col-6">
                                    <ul class="list-unstyled small">
                                        <li><i class="fas fa-id-card text-success"></i> Company PAN</li>
                                        <li><i class="fas fa-certificate text-primary"></i> GST Certificate</li>
                                        <li><i class="fas fa-money-check text-secondary"></i> Cancelled Cheque</li>
                                        <li><i class="fas fa-file-contract text-dark"></i> Registration Cert</li>
                                        <li><i class="fas fa-handshake text-warning"></i> Partnership Deed</li>
                                        <li><i class="fas fa-scroll text-info"></i> MOA</li>
                                    </ul>
                                </div>
                                <div class="col-6">
                                    <ul class="list-unstyled small">
                                        <li><i class="fas fa-scroll text-success"></i> AOA</li>
                                        <li><i class="fas fa-gavel text-primary"></i> Board Resolution</li>
                                        <li><i class="fas fa-certificate text-secondary"></i> CIN Certificate</li>
                                        <li><i class="fas fa-hands-helping text-warning"></i> Trust Deed</li>
                                        <li><i class="fas fa-users text-info"></i> Society Reg</li>
                                        <li><i class="fas fa-vote-yea text-dark"></i> Society Resolution</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <small>
                                <i class="fas fa-info-circle"></i>
                                <strong>18 Document Types Supported!</strong> The system automatically groups individual documents by person name (👤) and company documents by company name (🏢). Manual classification available for all types.
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4" id="resultsSection" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-file-alt"></i> Extraction Results</h5>
                        <div>
                            <button class="btn btn-sm btn-outline-primary" onclick="downloadJSON()">
                                <i class="fas fa-download"></i> Download JSON
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" onclick="copyToClipboard()">
                                <i class="fas fa-copy"></i> Copy
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="resultsContainer"></div>
                        
                        <h6 class="mt-4">JSON Output:</h6>
                        <div class="json-output" id="jsonOutput"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let selectedFiles = [];
        let extractionResults = [];

        // File upload handling
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const fileList = document.getElementById('fileList');

        uploadArea.addEventListener('click', () => fileInput.click());
        uploadArea.addEventListener('dragover', handleDragOver);
        uploadArea.addEventListener('drop', handleDrop);
        uploadArea.addEventListener('dragleave', handleDragLeave);
        fileInput.addEventListener('change', handleFileSelect);

        function handleDragOver(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            addFiles(files);
        }

        function handleFileSelect(e) {
            const files = Array.from(e.target.files);
            addFiles(files);
        }

        function addFiles(files) {
            files.forEach(file => {
                if (file.type.startsWith('image/') || file.type === 'application/pdf') {
                    selectedFiles.push(file);
                }
            });
            updateFileList();
        }

        function updateFileList() {
            fileList.innerHTML = '';
            selectedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'card mb-2';
                fileItem.innerHTML = `
                    <div class="card-body p-2">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-file-image text-primary me-2"></i>
                            <span class="flex-grow-1">${file.name}</span>
                            <small class="text-muted me-2">${(file.size / 1024).toFixed(1)} KB</small>
                            <button class="btn btn-sm btn-outline-danger" onclick="removeFile(${index})">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                `;
                fileList.appendChild(fileItem);
            });
        }

        function removeFile(index) {
            selectedFiles.splice(index, 1);
            updateFileList();
        }

        // Form submission
        document.getElementById('uploadForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            if (selectedFiles.length === 0) {
                alert('Please select at least one file');
                return;
            }

            const formData = new FormData();
            selectedFiles.forEach(file => {
                formData.append('files', file);
            });

            document.getElementById('loadingDiv').style.display = 'block';
            document.getElementById('processBtn').disabled = true;

            try {
                const response = await fetch('/upload', {
                    method: 'POST',
                    body: formData
                });

                const results = await response.json();
                extractionResults = results;
                displayResults(results);
                
            } catch (error) {
                console.error('Error:', error);
                alert('Error processing documents. Please try again.');
            } finally {
                document.getElementById('loadingDiv').style.display = 'none';
                document.getElementById('processBtn').disabled = false;
            }
        });

        function displayResults(results) {
            const container = document.getElementById('resultsContainer');
            const jsonOutput = document.getElementById('jsonOutput');
            
            container.innerHTML = '';
            
            results.forEach((group, groupIndex) => {
                const personName = Object.keys(group)[0];
                const documents = group[personName].documents;
                
                const groupCard = document.createElement('div');
                groupCard.className = 'result-card card mb-3';
                groupCard.innerHTML = `
                    <div class="card-header">
                        <h6 class="mb-0">
                            ${personName}
                            <span class="badge bg-secondary ms-2">${documents.length} document(s)</span>
                        </h6>
                    </div>
                    <div class="card-body">
                        ${documents.map(doc => `
                            <div class="border rounded p-3 mb-2 ${doc['Document Type'] === 'Unknown' ? 'unknown-document' : ''}">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <strong>${doc.filename}</strong>
                                    <div class="d-flex align-items-center gap-2">
                                        <button class="btn btn-sm btn-outline-primary view-file-btn"
                                                onclick="viewFile('${doc.filename}')"
                                                title="View original file">
                                            <i class="fas fa-eye"></i> View file
                                        </button>
                                        <span class="badge ${getDocTypeBadgeClass(doc['Document Type'])} document-type-badge">
                                            ${doc['Document Type']}
                                        </span>
                                    </div>
                                </div>

                                ${doc['Manual_Classification_Needed'] ? `
                                    <div class="suggestions-box">
                                        <h6><i class="fas fa-question-circle"></i> Document Not Recognized</h6>
                                        <p>The system couldn't automatically identify this document. Please help by selecting the correct type:</p>

                                        ${doc.Suggestions && doc.Suggestions.possible_types.length > 0 ? `
                                            <div class="mb-2">
                                                <strong>Possible types based on content:</strong>
                                                ${doc.Suggestions.possible_types.map(suggestion => `
                                                    <button class="btn btn-sm btn-outline-primary manual-classify-btn"
                                                            onclick="manualClassify('${doc.filename}', '${suggestion.type}', \`${doc['Raw Text']}\`)">
                                                        ${suggestion.type} (${suggestion.confidence} matches)
                                                    </button>
                                                `).join('')}
                                            </div>
                                        ` : ''}

                                        <div class="mb-2">
                                            <strong>Or select manually:</strong><br>
                                            <div class="mb-2">
                                                <small><strong>👤 Individual Documents:</strong></small><br>
                                                <button class="btn btn-sm btn-outline-success manual-classify-btn" onclick="manualClassify('${doc.filename}', 'PAN Card', \`${doc['Raw Text']}\`)">PAN Card</button>
                                                <button class="btn btn-sm btn-outline-info manual-classify-btn" onclick="manualClassify('${doc.filename}', 'Aadhaar Card', \`${doc['Raw Text']}\`)">Aadhaar Card</button>
                                                <button class="btn btn-sm btn-outline-info manual-classify-btn" onclick="manualClassify('${doc.filename}', 'Aadhaar XML', \`${doc['Raw Text']}\`)">Aadhaar XML</button>
                                                <button class="btn btn-sm btn-outline-warning manual-classify-btn" onclick="manualClassify('${doc.filename}', 'Driving License', \`${doc['Raw Text']}\`)">Driving License</button>
                                                <button class="btn btn-sm btn-outline-primary manual-classify-btn" onclick="manualClassify('${doc.filename}', 'Passport', \`${doc['Raw Text']}\`)">Passport</button>
                                                <button class="btn btn-sm btn-outline-secondary manual-classify-btn" onclick="manualClassify('${doc.filename}', 'Voter ID', \`${doc['Raw Text']}\`)">Voter ID</button>
                                            </div>
                                            <div class="mb-2">
                                                <small><strong>🏢 Company Documents:</strong></small><br>
                                                <button class="btn btn-sm btn-outline-success manual-classify-btn" onclick="manualClassify('${doc.filename}', 'Company PAN Card', \`${doc['Raw Text']}\`)">Company PAN</button>
                                                <button class="btn btn-sm btn-outline-primary manual-classify-btn" onclick="manualClassify('${doc.filename}', 'GST Certificate', \`${doc['Raw Text']}\`)">GST Certificate</button>
                                                <button class="btn btn-sm btn-outline-secondary manual-classify-btn" onclick="manualClassify('${doc.filename}', 'Cancelled Cheque', \`${doc['Raw Text']}\`)">Cancelled Cheque</button>
                                                <button class="btn btn-sm btn-outline-dark manual-classify-btn" onclick="manualClassify('${doc.filename}', 'Registration Certificate', \`${doc['Raw Text']}\`)">Registration Cert</button>
                                                <button class="btn btn-sm btn-outline-warning manual-classify-btn" onclick="manualClassify('${doc.filename}', 'Partnership Deed', \`${doc['Raw Text']}\`)">Partnership Deed</button>
                                                <button class="btn btn-sm btn-outline-info manual-classify-btn" onclick="manualClassify('${doc.filename}', 'Memorandum Of Association', \`${doc['Raw Text']}\`)">MOA</button>
                                                <button class="btn btn-sm btn-outline-success manual-classify-btn" onclick="manualClassify('${doc.filename}', 'Article Of Association', \`${doc['Raw Text']}\`)">AOA</button>
                                                <button class="btn btn-sm btn-outline-primary manual-classify-btn" onclick="manualClassify('${doc.filename}', 'Board Resolution', \`${doc['Raw Text']}\`)">Board Resolution</button>
                                                <button class="btn btn-sm btn-outline-secondary manual-classify-btn" onclick="manualClassify('${doc.filename}', 'CIN Certificate', \`${doc['Raw Text']}\`)">CIN Certificate</button>
                                                <button class="btn btn-sm btn-outline-warning manual-classify-btn" onclick="manualClassify('${doc.filename}', 'Trust Deed', \`${doc['Raw Text']}\`)">Trust Deed</button>
                                                <button class="btn btn-sm btn-outline-info manual-classify-btn" onclick="manualClassify('${doc.filename}', 'Society Registration Certificate', \`${doc['Raw Text']}\`)">Society Reg</button>
                                                <button class="btn btn-sm btn-outline-dark manual-classify-btn" onclick="manualClassify('${doc.filename}', 'Society Resolution', \`${doc['Raw Text']}\`)">Society Resolution</button>
                                            </div>
                                        </div>

                                        ${doc.Suggestions && doc.Suggestions.detected_patterns.length > 0 ? `
                                            <div class="mb-2">
                                                <small><strong>Detected patterns:</strong> ${doc.Suggestions.detected_patterns.join(', ')}</small>
                                            </div>
                                        ` : ''}

                                        <button class="btn btn-sm btn-outline-primary" onclick="showReprocessOptions('${doc.filename}')">
                                            <i class="fas fa-magic"></i> Auto-Enhance Image
                                        </button>
                                    </div>
                                ` : ''}

                                ${Object.entries(doc).filter(([key]) =>
                                    key !== 'filename' &&
                                    key !== 'Raw Text' &&
                                    key !== 'Suggestions' &&
                                    key !== 'Manual_Classification_Needed'
                                ).map(([key, value]) => `
                                    <div class="row mb-1">
                                        <div class="col-4"><strong>${key}:</strong></div>
                                        <div class="col-8">${value}</div>
                                    </div>
                                `).join('')}
                            </div>
                        `).join('')}
                    </div>
                `;
                container.appendChild(groupCard);
            });
            
            jsonOutput.textContent = JSON.stringify(results, null, 2);
            document.getElementById('resultsSection').style.display = 'block';
        }

        function getDocTypeBadgeClass(docType) {
            const classes = {
                'PAN Card': 'bg-success',
                'Aadhaar Card': 'bg-info',
                'Driving License': 'bg-warning',
                'Passport': 'bg-primary',
                'Voter ID': 'bg-secondary',
                'Unknown': 'bg-dark'
            };
            return classes[docType] || 'bg-dark';
        }

        function downloadJSON() {
            const dataStr = JSON.stringify(extractionResults, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'document_extraction_results.json';
            link.click();
            URL.revokeObjectURL(url);
        }

        function copyToClipboard() {
            const jsonText = JSON.stringify(extractionResults, null, 2);
            navigator.clipboard.writeText(jsonText).then(() => {
                alert('JSON copied to clipboard!');
            });
        }

        async function manualClassify(filename, docType, rawText) {
            try {
                const response = await fetch('/manual-classify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        filename: filename,
                        document_type: docType,
                        raw_text: rawText
                    })
                });

                const result = await response.json();

                // Update the results with the new classification
                updateDocumentInResults(filename, result);

                alert(`Document reclassified as ${docType}!`);

            } catch (error) {
                console.error('Error:', error);
                alert('Error reclassifying document. Please try again.');
            }
        }

        function updateDocumentInResults(filename, newResult) {
            // Find and update the document in extractionResults
            for (let group of extractionResults) {
                for (let personKey in group) {
                    let documents = group[personKey].documents;
                    for (let i = 0; i < documents.length; i++) {
                        if (documents[i].filename === filename) {
                            documents[i] = newResult;
                            // Re-display results
                            displayResults(extractionResults);
                            return;
                        }
                    }
                }
            }
        }

        async function autoEnhanceImage(filename) {
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('reprocessModal'));
            modal.hide();

            try {
                // Show enhanced processing indicator
                const processingMessage = document.createElement('div');
                processingMessage.className = 'alert alert-primary';
                processingMessage.innerHTML = `
                    <div class="d-flex align-items-center">
                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                        <div>
                            <strong>🚀 Auto-enhancing ${filename}...</strong><br>
                            <small id="enhancement-status">Trying different processing methods automatically...</small>
                        </div>
                    </div>
                `;
                document.getElementById('resultsContainer').prepend(processingMessage);

                // Update status periodically to show progress
                const statusElement = document.getElementById('enhancement-status');
                const methods = ['Gentle Processing', 'Standard Processing', 'Aggressive Processing', 'Skew Correction', 'Perspective Correction', 'Super Enhancement'];
                let currentMethodIndex = 0;

                const statusInterval = setInterval(() => {
                    if (currentMethodIndex < methods.length) {
                        statusElement.textContent = `Trying ${methods[currentMethodIndex]}...`;
                        currentMethodIndex++;
                    }
                }, 2000);

                const response = await fetch('/auto-enhance', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        filename: filename
                    })
                });

                const result = await response.json();

                // Clear status interval
                clearInterval(statusInterval);

                // Remove processing message
                processingMessage.remove();

                if (response.ok) {
                    // Update the results
                    updateDocumentInResults(filename, result);

                    // Show success message with details
                    const successMessage = document.createElement('div');
                    successMessage.className = 'alert alert-success alert-dismissible fade show';
                    successMessage.innerHTML = `
                        <div class="d-flex align-items-center">
                            <i class="fas fa-check-circle me-2"></i>
                            <div>
                                <strong>✅ Auto-enhancement successful!</strong><br>
                                <small>
                                    Best method: <strong>${result['Processing Method']}</strong>
                                    ${result['Quality Score'] ? `(Quality Score: ${Math.round(result['Quality Score'])}/100)` : ''}
                                    <br>Methods tried: ${result.methods_tried ? result.methods_tried.join(' → ') : 'Multiple methods'}
                                </small>
                            </div>
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    document.getElementById('resultsContainer').prepend(successMessage);

                    // Auto-dismiss after 5 seconds
                    setTimeout(() => {
                        if (successMessage.parentNode) {
                            successMessage.remove();
                        }
                    }, 5000);
                } else {
                    throw new Error(result.error || 'Unknown error');
                }

            } catch (error) {
                console.error('Error:', error);

                // Remove processing message if it exists
                const processingMsg = document.querySelector('.alert-primary');
                if (processingMsg) processingMsg.remove();

                // Show error message
                const errorMessage = document.createElement('div');
                errorMessage.className = 'alert alert-danger alert-dismissible fade show';
                errorMessage.innerHTML = `
                    <i class="fas fa-exclamation-triangle"></i> Auto-enhancement failed: ${error.message}
                    <br><small>You can try manual method selection from the advanced options.</small>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                document.getElementById('resultsContainer').prepend(errorMessage);
            }
        }

        function showReprocessOptions(filename) {
            const modal = `
                <div class="modal fade" id="reprocessModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Reprocess Image</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="text-center mb-4">
                                    <h5 class="text-primary">🚀 Automatic Image Enhancement</h5>
                                    <p class="text-muted">Let the system automatically try all processing methods until it finds the best result for your image.</p>
                                </div>

                                <div class="d-grid gap-3 mb-4">
                                    <button class="btn btn-primary btn-lg" onclick="autoEnhanceImage('${filename}')">
                                        <i class="fas fa-magic"></i> Auto-Enhance Image
                                        <br><small>Tries all methods automatically until best result is found</small>
                                    </button>
                                </div>

                                <div class="alert alert-info">
                                    <small>
                                        <i class="fas fa-info-circle"></i>
                                        <strong>How it works:</strong> The system will automatically try different processing methods in order:
                                        <br>• Gentle → Standard → Aggressive → Skew Correction → Perspective Correction → Super Enhancement
                                        <br>• Stops when a good result is found or all methods are tried
                                        <br>• Shows you which method worked best
                                    </small>
                                </div>

                                <details class="mt-3">
                                    <summary class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-cog"></i> Manual Method Selection (Advanced)
                                    </summary>
                                    <div class="mt-3">
                                        <div class="mb-3">
                                            <h6 class="text-primary">📱 Basic Methods</h6>
                                            <div class="d-grid gap-2">
                                                <button class="btn btn-outline-primary btn-sm" onclick="reprocessImage('${filename}', 'gentle')">
                                                    <i class="fas fa-feather"></i> Gentle Processing
                                                </button>
                                                <button class="btn btn-outline-warning btn-sm" onclick="reprocessImage('${filename}', 'standard')">
                                                    <i class="fas fa-adjust"></i> Standard Processing
                                                </button>
                                                <button class="btn btn-outline-danger btn-sm" onclick="reprocessImage('${filename}', 'aggressive')">
                                                    <i class="fas fa-fire"></i> Aggressive Processing
                                                </button>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <h6 class="text-success">🔧 Advanced Methods</h6>
                                            <div class="d-grid gap-2">
                                                <button class="btn btn-outline-info btn-sm" onclick="reprocessImage('${filename}', 'skew_correction')">
                                                    <i class="fas fa-undo"></i> Skew Correction
                                                </button>
                                                <button class="btn btn-outline-secondary btn-sm" onclick="reprocessImage('${filename}', 'perspective_correction')">
                                                    <i class="fas fa-cube"></i> Perspective Correction
                                                </button>
                                                <button class="btn btn-outline-dark btn-sm" onclick="reprocessImage('${filename}', 'super_enhancement')">
                                                    <i class="fas fa-magic"></i> Super Enhancement
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </details>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal if any
            const existingModal = document.getElementById('reprocessModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Add modal to body
            document.body.insertAdjacentHTML('beforeend', modal);

            // Show modal
            const modalElement = new bootstrap.Modal(document.getElementById('reprocessModal'));
            modalElement.show();
        }

        async function reprocessImage(filename, method) {
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('reprocessModal'));
            modal.hide();

            try {
                // Show processing indicator
                const processingMessage = document.createElement('div');
                processingMessage.className = 'alert alert-info';
                processingMessage.innerHTML = `
                    <div class="d-flex align-items-center">
                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                        Reprocessing ${filename} with ${method} method...
                    </div>
                `;
                document.getElementById('resultsContainer').prepend(processingMessage);

                const response = await fetch('/reprocess-existing', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        filename: filename,
                        method: method
                    })
                });

                const result = await response.json();

                // Remove processing message
                processingMessage.remove();

                if (response.ok) {
                    // Update the results
                    updateDocumentInResults(filename, result);

                    // Show success message
                    const successMessage = document.createElement('div');
                    successMessage.className = 'alert alert-success alert-dismissible fade show';
                    successMessage.innerHTML = `
                        <i class="fas fa-check-circle"></i> Successfully reprocessed ${filename} with ${method} method!
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    document.getElementById('resultsContainer').prepend(successMessage);

                    // Auto-dismiss after 3 seconds
                    setTimeout(() => {
                        if (successMessage.parentNode) {
                            successMessage.remove();
                        }
                    }, 3000);
                } else {
                    throw new Error(result.error || 'Unknown error');
                }

            } catch (error) {
                console.error('Error:', error);

                // Remove processing message if it exists
                const processingMsg = document.querySelector('.alert-info');
                if (processingMsg) processingMsg.remove();

                // Show error message
                const errorMessage = document.createElement('div');
                errorMessage.className = 'alert alert-danger alert-dismissible fade show';
                errorMessage.innerHTML = `
                    <i class="fas fa-exclamation-triangle"></i> Error reprocessing image: ${error.message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                document.getElementById('resultsContainer').prepend(errorMessage);
            }
        }

        function viewFile(filename) {
            // Show the modal
            const modal = new bootstrap.Modal(document.getElementById('fileViewModal'));
            const modalImage = document.getElementById('modalImage');
            const modalFileName = document.getElementById('modalFileName');
            const downloadBtn = document.getElementById('downloadImageBtn');
            const loadingSpinner = document.getElementById('imageLoadingSpinner');
            const errorMessage = document.getElementById('imageErrorMessage');

            // Set filename in modal title
            modalFileName.textContent = filename;

            // Show loading spinner
            loadingSpinner.classList.remove('d-none');
            modalImage.classList.add('d-none');
            errorMessage.classList.add('d-none');

            // Set image source and download link
            const imageUrl = `/uploads/${filename}`;
            modalImage.src = imageUrl;
            downloadBtn.href = imageUrl;
            downloadBtn.download = filename;

            // Handle image load events
            modalImage.onload = function() {
                loadingSpinner.classList.add('d-none');
                modalImage.classList.remove('d-none');
            };

            modalImage.onerror = function() {
                loadingSpinner.classList.add('d-none');
                errorMessage.classList.remove('d-none');
                console.error('Failed to load image:', imageUrl);
            };

            // Show the modal
            modal.show();
        }
    </script>

    <!-- File View Modal -->
    <div class="modal fade" id="fileViewModal" tabindex="-1" aria-labelledby="fileViewModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="fileViewModalLabel">
                        <i class="fas fa-file-image"></i> <span id="modalFileName">Document</span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <div id="imageLoadingSpinner" class="d-none">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading image...</p>
                    </div>
                    <img id="modalImage" class="img-fluid rounded shadow" style="max-height: 70vh;" alt="Document Image">
                    <div id="imageErrorMessage" class="alert alert-danger d-none" role="alert">
                        <i class="fas fa-exclamation-triangle"></i> Failed to load image. The file may have been moved or deleted.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> Close
                    </button>
                    <a id="downloadImageBtn" href="#" download class="btn btn-primary">
                        <i class="fas fa-download"></i> Download
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
