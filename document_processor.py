import pytesseract
from PIL import Image
import cv2
import numpy as np
import re
import os
from document_types import DocumentTypeManager

class DocumentProcessor:
    def __init__(self):
        self.doc_type_manager = DocumentTypeManager()
        # Configure tesseract path for Windows
        import platform
        if platform.system() == 'Windows':
            # Common Tesseract installation paths on Windows
            possible_paths = [
                r'C:\Program Files\Tesseract-OCR\tesseract.exe',
                r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
                r'C:\Users\<USER>\AppData\Local\Tesseract-OCR\tesseract.exe'.format(os.environ.get('USERNAME', '')),
                r'C:\tesseract\tesseract.exe'
            ]

            for path in possible_paths:
                if os.path.exists(path):
                    pytesseract.pytesseract.tesseract_cmd = path
                    print(f"Found Tesseract at: {path}")
                    break
            else:
                print("Warning: Tesseract not found in common locations. Please set the path manually.")
                # Try to use tesseract from PATH
                try:
                    import subprocess
                    result = subprocess.run(['tesseract', '--version'], capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        print("Using Tesseract from system PATH")
                    else:
                        print("Tesseract not available in PATH either")
                except Exception as e:
                    print(f"Could not find Tesseract in PATH: {e}")
    
    def preprocess_image(self, image_path, method='standard'):
        """Preprocess image for better OCR results with multiple methods"""
        # Read image
        img = cv2.imread(image_path)

        if method == 'aggressive':
            return self._aggressive_preprocessing(img)
        elif method == 'gentle':
            return self._gentle_preprocessing(img)
        elif method == 'pan_specific':
            return self._pan_specific_preprocessing(img)
        elif method == 'poor_quality':
            return self._poor_quality_preprocessing(img)
        elif method == 'extreme_enhancement':
            return self._extreme_enhancement_preprocessing(img)
        elif method == 'noise_reduction':
            return self._noise_reduction_preprocessing(img)
        else:
            return self._standard_preprocessing(img)

    def _standard_preprocessing(self, img):
        """Standard preprocessing method"""
        # Resize image if too small
        height, width = img.shape[:2]
        if height < 600 or width < 800:
            scale_factor = max(800/width, 600/height)
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            img = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # Apply CLAHE for better contrast
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)

        # Apply noise reduction
        denoised = cv2.medianBlur(enhanced, 3)

        # Apply threshold
        _, thresh = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        return thresh

    def _gentle_preprocessing(self, img):
        """Gentle preprocessing for high-quality images"""
        # Minimal processing for clear images
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # Slight enhancement
        clahe = cv2.createCLAHE(clipLimit=1.5, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)

        return enhanced

    def _aggressive_preprocessing(self, img):
        """Aggressive preprocessing for poor quality images"""
        # Resize significantly if small
        height, width = img.shape[:2]
        if height < 800 or width < 1000:
            scale_factor = max(1000/width, 800/height)
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            img = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # Strong contrast enhancement
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)

        # Multiple noise reduction steps
        denoised = cv2.medianBlur(enhanced, 5)
        denoised = cv2.bilateralFilter(denoised, 9, 75, 75)

        # Sharpening
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(denoised, -1, kernel)

        # Adaptive threshold
        thresh = cv2.adaptiveThreshold(sharpened, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                     cv2.THRESH_BINARY, 11, 2)

        # Morphological operations
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        processed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        processed = cv2.morphologyEx(processed, cv2.MORPH_OPEN, kernel)

        return processed

    def _pan_specific_preprocessing(self, img):
        """PAN card specific preprocessing"""
        # PAN cards often have specific color schemes and layouts
        # Resize for better OCR
        height, width = img.shape[:2]
        if height < 800 or width < 1200:
            scale_factor = max(1200/width, 800/height)
            new_width = int(width * scale_factor)
            new_height = int(height * scale_factor)
            img = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # PAN cards often have blue/green backgrounds, enhance contrast
        clahe = cv2.createCLAHE(clipLimit=2.5, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)

        # Noise reduction
        denoised = cv2.medianBlur(enhanced, 3)

        # Adaptive threshold works well for PAN cards
        thresh = cv2.adaptiveThreshold(denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                     cv2.THRESH_BINARY, 11, 2)

        # Clean up
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 1))
        processed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)

        return processed

    def _poor_quality_preprocessing(self, img):
        """Preprocessing specifically for poor quality images"""
        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # Resize image to improve OCR (make it larger)
        height, width = gray.shape
        scale_factor = max(2.0, 1500 / max(height, width))
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)
        resized = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

        # Strong noise reduction
        denoised = cv2.bilateralFilter(resized, 9, 75, 75)

        # Enhance contrast using CLAHE
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        enhanced = clahe.apply(denoised)

        # Multiple threshold attempts
        # Try Otsu's thresholding
        _, thresh1 = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # Try adaptive threshold
        thresh2 = cv2.adaptiveThreshold(enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                       cv2.THRESH_BINARY, 15, 10)

        # Combine both thresholds
        combined = cv2.bitwise_and(thresh1, thresh2)

        # Morphological operations to clean up
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        cleaned = cv2.morphologyEx(combined, cv2.MORPH_CLOSE, kernel)
        cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel)

        return cleaned

    def _extreme_enhancement_preprocessing(self, img):
        """Extreme enhancement for very poor quality images"""
        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # Aggressive upscaling
        height, width = gray.shape
        scale_factor = max(3.0, 2000 / max(height, width))
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)
        resized = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)

        # Multiple denoising passes
        denoised = cv2.fastNlMeansDenoising(resized, None, 10, 7, 21)
        denoised = cv2.medianBlur(denoised, 3)

        # Extreme contrast enhancement
        clahe = cv2.createCLAHE(clipLimit=4.0, tileGridSize=(4,4))
        enhanced = clahe.apply(denoised)

        # Sharpening
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(enhanced, -1, kernel)

        # Adaptive threshold with larger neighborhood
        thresh = cv2.adaptiveThreshold(sharpened, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                     cv2.THRESH_BINARY, 21, 15)

        return thresh

    def _noise_reduction_preprocessing(self, img):
        """Focus on noise reduction for scanned documents"""
        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # Moderate upscaling
        height, width = gray.shape
        scale_factor = max(1.5, 1200 / max(height, width))
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)
        resized = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

        # Advanced denoising
        denoised = cv2.fastNlMeansDenoising(resized, None, 8, 7, 21)

        # Gaussian blur to smooth
        blurred = cv2.GaussianBlur(denoised, (3, 3), 0)

        # Enhance contrast
        enhanced = cv2.convertScaleAbs(blurred, alpha=1.2, beta=10)

        # Threshold
        _, thresh = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        return thresh

    def clean_mixed_language_text(self, text):
        """Clean mixed Hindi/English OCR text to extract English content"""
        import re

        # Split into lines
        lines = text.split('\n')
        cleaned_lines = []

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Remove Hindi characters (Devanagari script)
            # Keep only English characters, numbers, and common punctuation
            english_only = re.sub(r'[^\x00-\x7F]+', ' ', line)

            # Clean up extra spaces
            english_only = re.sub(r'\s+', ' ', english_only).strip()

            # Skip lines that are too short or just punctuation
            if len(english_only) > 2 and not re.match(r'^[^\w]*$', english_only):
                cleaned_lines.append(english_only)

        return '\n'.join(cleaned_lines)

    def _calculate_text_quality(self, text):
        """Calculate quality score for extracted text"""
        if not text or len(text.strip()) == 0:
            return 0.0

        # Count different types of characters
        total_chars = len(text)
        alphanumeric_chars = sum(1 for c in text if c.isalnum())
        letter_chars = sum(1 for c in text if c.isalpha())
        digit_chars = sum(1 for c in text if c.isdigit())
        space_chars = sum(1 for c in text if c.isspace())

        # Calculate ratios
        alphanumeric_ratio = alphanumeric_chars / total_chars if total_chars > 0 else 0
        letter_ratio = letter_chars / total_chars if total_chars > 0 else 0

        # Penalize text with too many special characters or gibberish
        if alphanumeric_ratio < 0.3:  # Less than 30% alphanumeric
            return 0.1

        # Reward text with good letter content
        quality_score = alphanumeric_ratio * 0.7 + letter_ratio * 0.3

        # Bonus for having both letters and numbers (typical in ID documents)
        if letter_chars > 0 and digit_chars > 0:
            quality_score += 0.1

        # Bonus for reasonable length
        if 50 <= len(text.strip()) <= 1000:
            quality_score += 0.1

        return min(1.0, quality_score)

    def get_optimal_psm_modes(self, image_path):
        """Determine optimal PSM modes based on image characteristics"""
        try:
            # Analyze image to determine best PSM modes
            img = cv2.imread(image_path)
            height, width = img.shape[:2]
            aspect_ratio = width / height

            # Different PSM modes for different document types and layouts
            psm_modes = []

            # For card-like documents (PAN, Aadhaar) - typically wider than tall
            if aspect_ratio > 1.4:  # Landscape orientation
                psm_modes.extend([
                    '--psm 6',   # Uniform block of text (good for cards)
                    '--psm 4',   # Single column of text
                    '--psm 3',   # Fully automatic page segmentation
                    '--psm 8',   # Single word
                    '--psm 7',   # Single text line
                ])
            # For portrait documents (licenses, passports)
            elif aspect_ratio < 0.8:  # Portrait orientation
                psm_modes.extend([
                    '--psm 4',   # Single column of text
                    '--psm 3',   # Fully automatic page segmentation
                    '--psm 6',   # Uniform block of text
                    '--psm 1',   # Automatic page segmentation with OSD
                ])
            # For square-ish documents
            else:
                psm_modes.extend([
                    '--psm 3',   # Fully automatic page segmentation
                    '--psm 6',   # Uniform block of text
                    '--psm 4',   # Single column of text
                    '--psm 8',   # Single word
                ])

            return psm_modes

        except Exception as e:
            print(f"Error analyzing image for PSM selection: {e}")
            # Fallback to comprehensive list
            return ['--psm 3', '--psm 6', '--psm 4', '--psm 8', '--psm 7', '--psm 1']

    def extract_text_from_image(self, image_path):
        """Extract text from image using adaptive preprocessing and PSM selection"""
        try:
            best_text = ""
            best_length = 0
            best_method = ""

            # Get optimal PSM modes for this image
            optimal_psm_modes = self.get_optimal_psm_modes(image_path)

            # Comprehensive method combinations
            preprocessing_methods = [
                'original_eng_hin',     # Original image with mixed languages
                'original_eng',         # Original image English only
                'gentle',              # Minimal preprocessing
                'standard',            # Standard preprocessing
                'aggressive',          # Heavy preprocessing
                'pan_specific',        # PAN card specific
                'poor_quality',        # Poor quality image enhancement
                'extreme_enhancement', # Extreme enhancement for very poor images
                'noise_reduction',     # Focus on noise reduction
            ]

            # Try each preprocessing method with optimal PSM modes
            for method in preprocessing_methods:
                for psm_config in optimal_psm_modes:
                    try:
                        # Handle different preprocessing methods
                        if method == 'original_eng_hin':
                            # Use original image without preprocessing, mixed languages
                            pil_img = Image.open(image_path)
                            text = pytesseract.image_to_string(pil_img, lang='eng+hin', config=psm_config)
                        elif method == 'original_eng':
                            # Use original image without preprocessing, English only
                            pil_img = Image.open(image_path)
                            text = pytesseract.image_to_string(pil_img, lang='eng', config=psm_config)
                        else:
                            # Preprocess image with current method
                            processed_img = self.preprocess_image(image_path, method=method)
                            pil_img = Image.fromarray(processed_img)
                            text = pytesseract.image_to_string(pil_img, lang='eng', config=psm_config)

                        # Clean and evaluate the text
                        cleaned_text = self.clean_mixed_language_text(text)
                        text_length = len(cleaned_text.strip())

                        # Quality scoring: prefer results with more alphanumeric content
                        quality_score = self._calculate_text_quality(cleaned_text)
                        combined_score = text_length * quality_score

                        # Keep the best result based on combined score
                        if combined_score > best_length:
                            best_text = cleaned_text
                            best_length = combined_score
                            best_method = f"{method} + {psm_config}"

                        # Early exit if we get very good results
                        if text_length > 200 and quality_score > 0.7:
                            print(f"Early exit with good result: {text_length} chars, quality: {quality_score:.2f}")
                            break

                    except Exception as e:
                        print(f"Method {method} with {psm_config} failed: {e}")
                        continue

                # Break outer loop if we found good results
                if best_length > 200 * 0.7:  # Good length * quality threshold
                    break

            # Try original image with different PSM modes if preprocessing didn't work well
            if best_length < 50:  # Very poor results, try fallback
                fallback_psm_modes = ['--psm 3', '--psm 6', '--psm 4', '--psm 8']
                for psm_mode in fallback_psm_modes:
                    try:
                        original_img = Image.open(image_path)
                        original_text = pytesseract.image_to_string(original_img, lang='eng', config=psm_mode)
                        cleaned_fallback = self.clean_mixed_language_text(original_text)
                        fallback_quality = self._calculate_text_quality(cleaned_fallback)
                        fallback_score = len(cleaned_fallback.strip()) * fallback_quality

                        if fallback_score > best_length:
                            best_text = cleaned_fallback
                            best_method = f"fallback original + {psm_mode}"
                            best_length = fallback_score
                    except Exception as e:
                        print(f"Fallback method {psm_mode} failed: {e}")
                        continue

            # Convert score back to length for display
            actual_length = len(best_text.strip()) if best_text else 0
            print(f"Best OCR result: {actual_length} characters (score: {best_length:.1f}) using {best_method}")

            return best_text.strip() if best_text else ""

        except Exception as e:
            print(f"Error extracting text from {image_path}: {str(e)}")
            return ""
    
    def identify_document_type(self, text):
        """Identify document type with enhanced Aadhaar detection and poor quality handling"""
        doc_type, confidence = self.doc_type_manager.identify_document_type(text)

        # Enhanced Aadhaar detection for poor quality images
        if doc_type == 'Unknown':
            doc_type = self._enhanced_aadhaar_detection(text)

        return doc_type

    def _enhanced_aadhaar_detection(self, text):
        """Enhanced Aadhaar card detection for poor quality images"""
        aadhaar_indicators = 0
        text_lower = text.lower()

        # Strong Aadhaar indicators
        strong_indicators = [
            'government of india',
            'unique identification',
            'uidai',
            'aadhaar',
            'aadhar',
            'male',
            'female',
        ]

        # Pattern indicators
        patterns = [
            r'\b\d{4}\s*\d{4}\s*\d{4}\b',  # 12-digit number pattern
            r'dob\s*:?\s*\d{1,2}[/-]\d{1,2}[/-]\d{4}',  # DOB pattern
            r'(male|female)',  # Gender
            r'address\s*:',  # Address field
        ]

        # Check strong indicators
        for indicator in strong_indicators:
            if indicator in text_lower:
                aadhaar_indicators += 2

        # Check patterns
        import re
        for pattern in patterns:
            if re.search(pattern, text_lower):
                aadhaar_indicators += 1

        # If we have enough indicators, classify as Aadhaar
        if aadhaar_indicators >= 3:
            return 'Aadhaar Card'

        return 'Unknown'
    
    def extract_structured_data(self, text, doc_type):
        """Extract structured data using improved extraction methods"""
        structured_data = {}

        # Use improved extraction methods first (they are more accurate)
        if doc_type == 'PAN Card':
            structured_data = self.extract_pan_data(text)
        elif doc_type == 'Aadhaar Card':
            structured_data = self.extract_aadhaar_data(text)
        elif doc_type == 'Driving License':
            structured_data = self.extract_dl_data(text)
        elif doc_type == 'Passport':
            structured_data = self.extract_passport_data(text)
        elif doc_type == 'Voter ID':
            structured_data = self.extract_voter_data(text)

        # If improved methods didn't work, fallback to document type manager
        if not structured_data and doc_type != 'Unknown':
            structured_data = self.doc_type_manager.extract_structured_data(text, doc_type)

        return structured_data
    
    def extract_pan_data(self, text):
        """Extract PAN card specific data with improved accuracy and quality assessment"""
        data = {}
        quality_issues = []

        # Assess text quality
        text_quality = self._calculate_text_quality(text)
        is_poor_quality = text_quality < 0.7 or len(text.strip()) < 100

        if is_poor_quality:
            quality_issues.append("Poor image quality detected")

        # Clean the text
        lines = [line.strip() for line in text.split('\n') if line.strip()]

        # PAN Number - more robust pattern
        pan_patterns = [
            r'([A-Z]{5}[0-9]{4}[A-Z])',  # Standard PAN format
            r'PAN[:\s]*([A-Z]{5}[0-9]{4}[A-Z])',  # With PAN prefix
        ]
        for pattern in pan_patterns:
            pan_match = re.search(pattern, text)
            if pan_match:
                data['PAN Number'] = pan_match.group(1) if 'PAN' not in pattern else pan_match.group(1)
                break

        # Name extraction - improved for PAN cards
        name_candidates = []

        # Look for names in specific patterns - English only
        # Strategy: Find the person's name (not father's name)

        # Pattern 1: Explicit "Name" label - handle various formats
        name_patterns = [
            r'/\s*Name\s*\n([A-Z][A-Z\s]+?)(?:\s*[\'"]|$)',                # Slash + Name + newline (most common)
            r'(?:^|\n)\s*Name[:\s]*\n?\s*([A-Z][A-Z\s]+?)(?:\s*[/\n]|$)',  # Name on next line
            r'(?:^|\n)\s*Name[:\s]*([A-Z][A-Z\s]+?)(?:\s*[/\n]|Father)',   # Name on same line
            r'/\s*Name[:\s]*\n?\s*([A-Z][A-Z\s]+?)(?:\s*[/\n]|$)',        # With slash prefix
        ]

        for pattern in name_patterns:
            name_match = re.search(pattern, text, re.MULTILINE | re.IGNORECASE)
            if name_match:
                name = name_match.group(1).strip()
                # Clean up any trailing characters or symbols
                name = re.sub(r'[^\w\s].*$', '', name).strip()
                name = re.sub(r'\s+', ' ', name)
                if self._is_valid_pan_name(name):
                    name_candidates.append(name)
                    break

        # Pattern 2: Find standalone name lines that appear AFTER PAN number but BEFORE Father's section
        pan_number_found = False
        father_section_found = False

        for i, line in enumerate(lines):
            line = line.strip()

            # Track when we find PAN number
            if re.match(r'^[A-Z]{5}[0-9]{4}[A-Z]$', line):
                pan_number_found = True
                continue

            # Track when we reach Father's section
            if 'father' in line.lower():
                father_section_found = True
                break

            # Look for name lines after PAN but before Father's section
            if pan_number_found and not father_section_found:
                if (re.match(r'^[A-Z][A-Z\s]+$', line) and
                    2 <= len(line.split()) <= 4 and
                    len(line) >= 10 and
                    self._is_valid_pan_name(line)):
                    name_candidates.append(line)
                    break  # Take the first valid name after PAN number

        if name_candidates:
            # Prefer shorter, cleaner names
            best_name = min(name_candidates, key=len)
            data['Name'] = best_name

        # Father's Name - improved patterns based on actual text structure
        father_patterns = [
            r'/\s*Father[\'s]*\s*Name[^A-Z]*\n([A-Z][A-Z\s]+)',  # Simplified: slash + Father's Name + anything + newline + name
            r'Father[\'s]*\s*Name[^A-Z]*\n\s*([A-Z][A-Z\s]+)',  # Name on next line
            r'/\s*Father[\'s]*\s*Name\s*[-\s]*\s*\d*\s*\n([A-Z][A-Z\s]+?)(?:\s*[a-z]|$)',  # Original pattern
            r'(?:Father[\'s]*\s*Name)\s*[-\s]*\s*\d*[:\s]*([A-Z][A-Z\s]+?)(?:\s*[/\n]|$)',  # Name on same line
        ]

        for pattern in father_patterns:
            father_match = re.search(pattern, text, re.IGNORECASE | re.MULTILINE)
            if father_match:
                father_name = father_match.group(1).strip()
                # Clean up any trailing characters, numbers, or symbols BEFORE validation
                father_name = re.sub(r'\s*[a-z].*$', '', father_name).strip()  # Remove trailing lowercase text
                father_name = re.sub(r'\s*\d+.*$', '', father_name).strip()  # Remove trailing numbers
                father_name = re.sub(r'[^\w\s].*$', '', father_name).strip()  # Remove trailing special chars
                father_name = re.sub(r'\s+', ' ', father_name)

                # Only validate after cleaning
                if father_name and self._is_valid_pan_name(father_name):
                    data['Father\'s Name'] = father_name
                    break

        # Date of Birth - improved patterns
        dob_patterns = [
            r'(?:Date\s*of\s*Birth|DOB)[:\s]*r?\s*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',  # With label
            r'/\s*Date\s*of\s*Birth[:\s]*r?\s*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',     # With slash prefix
            r'(\d{1,2}[/-]\d{1,2}[/-]\d{4})',                                      # Standalone date
            r'(\d{1,2}\.\d{1,2}\.\d{4})',                                          # Dot separated
        ]
        for pattern in dob_patterns:
            dob_match = re.search(pattern, text, re.IGNORECASE)
            if dob_match:
                date_str = dob_match.group(1)
                # Validate the date format
                if self._is_valid_date(date_str):
                    data['Date of Birth'] = date_str
                    break

        # Add quality remarks if there are issues
        if quality_issues:
            data['Quality_Remarks'] = quality_issues

        # Add additional quality checks
        if len(data) <= 1:  # Only PAN number or no data
            if 'Quality_Remarks' not in data:
                data['Quality_Remarks'] = []
            data['Quality_Remarks'].append("Limited data extracted - poor image quality")

        return data

    def _is_valid_pan_name(self, name):
        """Check if a string looks like a valid PAN card name"""
        if not name or len(name) < 3:
            return False

        # PAN names are usually all caps
        if not name.isupper():
            return False

        words = name.split()
        if len(words) < 2 or len(words) > 5:
            return False

        # Check for invalid content
        invalid_words = ['INCOME', 'TAX', 'DEPARTMENT', 'GOVT', 'INDIA', 'PERMANENT', 'ACCOUNT', 'NUMBER']
        if any(word in invalid_words for word in words):
            return False

        # Check for numbers or special characters
        if re.search(r'[0-9]', name) or re.search(r'[^\w\s]', name):
            return False

        return True

    def _is_valid_date(self, date_str):
        """Check if a string looks like a valid date"""
        if not date_str:
            return False

        # Basic format check
        import re
        if not re.match(r'\d{1,2}[/-]\d{1,2}[/-]\d{4}', date_str):
            return False

        # Extract day, month, year
        parts = re.split(r'[/-]', date_str)
        if len(parts) != 3:
            return False

        try:
            day, month, year = map(int, parts)

            # Basic range checks
            if not (1 <= day <= 31):
                return False
            if not (1 <= month <= 12):
                return False
            if not (1900 <= year <= 2030):
                return False

            return True
        except ValueError:
            return False
    
    def extract_aadhaar_data(self, text):
        """Extract Aadhaar card specific data with improved pattern matching and quality assessment"""
        data = {}
        quality_issues = []

        # Assess text quality
        text_quality = self._calculate_text_quality(text)
        is_poor_quality = text_quality < 0.6 or len(text.strip()) < 100

        if is_poor_quality:
            quality_issues.append("Poor image quality detected")

        # Clean the text for better processing
        lines = [line.strip() for line in text.split('\n') if line.strip()]

        # Aadhaar Number - multiple patterns with enhanced detection
        aadhaar_patterns = [
            r'(\d{4}\s+\d{4}\s+\d{4})',  # Standard format with spaces
            r'(\d{4}\s*\d{4}\s*\d{4})',  # With or without spaces
            r'(\d{12})',                  # 12 digits together
            r'(\d{1,4}\s*\d{1,4}\s*\d{1,4})',  # Partial numbers for poor quality
        ]

        aadhaar_found = False
        for pattern in aadhaar_patterns:
            aadhaar_matches = re.findall(pattern, text)
            for match in aadhaar_matches:
                # Clean the match
                clean_number = re.sub(r'\s+', '', match)
                if len(clean_number) >= 10:  # At least 10 digits for partial recognition
                    if len(clean_number) == 12:
                        # Full Aadhaar number
                        formatted_number = f"{clean_number[:4]} {clean_number[4:8]} {clean_number[8:]}"
                        data['Aadhaar Number'] = formatted_number
                        aadhaar_found = True
                        break
                    elif len(clean_number) >= 10:
                        # Partial Aadhaar number
                        data['Aadhaar Number'] = match
                        quality_issues.append("Partial Aadhaar number extracted")
                        aadhaar_found = True
                        break
            if aadhaar_found:
                break
                break

        # Name extraction - improved logic for different Aadhaar formats
        name_candidates = []

        # Look for names in specific patterns - English only
        name_patterns = [
            # Pattern 1: After Name: keyword
            r'(?:name)[:\s]*([A-Z][a-z]+(?:\s+[A-Z][a-z]+){1,3})',
            # Pattern 2: Standalone name lines (2-4 words, proper case)
            r'^([A-Z][a-z]+(?:\s+[A-Z][a-z]+){1,3})$',
            # Pattern 3: After government headers, look for next proper name
            r'(?:government of india|aadhaar).*?\n(?:[^\n]*\n)*?\s*([A-Z][a-z]+(?:\s+[A-Z][a-z]+){1,3})\s*\n',
        ]

        for pattern in name_patterns:
            matches = re.findall(pattern, text, re.MULTILINE | re.IGNORECASE)
            for match in matches:
                # Clean and validate the name
                clean_name = re.sub(r'[^\w\s]', '', match).strip()
                if self._is_valid_name(clean_name):
                    name_candidates.append(clean_name)

        # Also check individual lines for names
        for i, line in enumerate(lines):
            line = line.strip()

            # Skip lines with common non-name content
            skip_words = ['government', 'india', 'aadhaar', 'authority', 'dob', 'address', 'male', 'female', 'year', 'unique', 'identification']
            if any(skip_word in line.lower() for skip_word in skip_words):
                continue

            # Skip lines that are too short or contain numbers/special chars
            if len(line) < 5 or re.search(r'[0-9:/]', line):
                continue

            # Check if line looks like a name (2-4 words, proper case)
            if re.match(r'^[A-Z][a-z]+(?:\s+[A-Z][a-z]+){1,3}$', line):
                if self._is_valid_name(line):
                    # Give higher priority to names that appear after headers
                    if i > 0 and any(header in lines[i-1].lower() for header in ['aadhaar', 'आधार']):
                        name_candidates.insert(0, line)  # Add to front
                    else:
                        name_candidates.append(line)

        # Select the best name candidate
        if name_candidates:
            # Prefer names that are 2-3 words long
            best_name = min(name_candidates, key=lambda x: abs(len(x.split()) - 2.5))
            data['Name'] = best_name

        # Date of Birth - multiple formats, English only
        dob_patterns = [
            r'(?:DOB|Birth)[:\s]*(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            r'(\d{1,2}[/-]\d{1,2}[/-]\d{4})',
            r'(\d{1,2}\.\d{1,2}\.\d{4})',
        ]
        for pattern in dob_patterns:
            dob_match = re.search(pattern, text, re.IGNORECASE)
            if dob_match:
                data['Date of Birth'] = dob_match.group(1)
                break

        # Gender - English only
        gender_patterns = [
            r'\b(male|female)\b',
            r'(?:gender)[:\s]*(male|female)',
        ]
        for pattern in gender_patterns:
            gender_match = re.search(pattern, text, re.IGNORECASE)
            if gender_match:
                gender = gender_match.group(1).lower()
                if gender == 'male':
                    data['Gender'] = 'Male'
                elif gender == 'female':
                    data['Gender'] = 'Female'
                break

        # Address - improved extraction
        address_patterns = [
            r'(?:Address|पता)[:\s]*(.+?)(?:\n\n|\n[A-Z]|\d{4}\s*\d{4})',
            r'(?:S/O|D/O|W/O)[:\s]*[^,\n]+,\s*(.+?)(?:\n\n|\n[A-Z])',
        ]
        for pattern in address_patterns:
            address_match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if address_match:
                address = address_match.group(1).strip()
                # Clean up the address
                address = re.sub(r'\s+', ' ', address)
                if len(address) > 10:  # Only if we got a reasonable address
                    data['Address'] = address
                    break

        # Add quality remarks if there are issues
        if quality_issues:
            data['Quality_Remarks'] = quality_issues

        # Add additional quality checks
        if len(data) <= 2:  # Only Aadhaar number and maybe one other field
            if 'Quality_Remarks' not in data:
                data['Quality_Remarks'] = []
            data['Quality_Remarks'].append("Limited data extracted - poor image quality")

        return data

    def _is_valid_name(self, name):
        """Check if a string looks like a valid person name"""
        if not name or len(name) < 3:
            return False

        words = name.split()
        if len(words) < 2 or len(words) > 4:
            return False

        # Check if all words start with capital letter
        if not all(word[0].isupper() for word in words):
            return False

        # Check for common non-name patterns
        invalid_patterns = [
            r'\d',  # Contains numbers
            r'[^\w\s]',  # Contains special characters
        ]

        for pattern in invalid_patterns:
            if re.search(pattern, name):
                return False

        # Check for invalid words that shouldn't be in names
        invalid_words = ['GOVERNMENT', 'INDIA', 'AADHAAR', 'AUTHORITY', 'UNIQUE', 'IDENTIFICATION', 'CARD', 'DOB', 'ADDRESS']
        name_upper = name.upper()
        if any(word in name_upper for word in invalid_words):
            return False

        # Check if words are reasonable length (not too short or too long)
        if any(len(word) < 2 or len(word) > 15 for word in words):
            return False

        return True

    def extract_dl_data(self, text):
        """Extract Driving License specific data"""
        data = {}

        # License Number
        dl_patterns = [
            r'DL[:\s]*([A-Z0-9]+)',
            r'License[:\s]*([A-Z0-9]+)',
        ]
        for pattern in dl_patterns:
            dl_match = re.search(pattern, text, re.IGNORECASE)
            if dl_match:
                data['License Number'] = dl_match.group(1)
                break

        # Name
        name_patterns = [
            r'Name[:\s]*([A-Z\s]+?)(?:\n|$)',
            r'^([A-Z][a-z]+(?:\s[A-Z][a-z]+)+)',
        ]
        for pattern in name_patterns:
            name_match = re.search(pattern, text, re.MULTILINE | re.IGNORECASE)
            if name_match:
                data['Name'] = name_match.group(1).strip()
                break

        return data

    def extract_passport_data(self, text):
        """Extract Passport specific data"""
        data = {}

        # Passport Number
        passport_match = re.search(r'([A-Z]\d{7})', text)
        if passport_match:
            data['Passport Number'] = passport_match.group(1)

        return data

    def extract_voter_data(self, text):
        """Extract Voter ID specific data"""
        data = {}

        # Voter ID Number
        voter_patterns = [
            r'([A-Z]{3}\d{7})',
            r'EPIC[:\s]*([A-Z0-9]+)',
        ]
        for pattern in voter_patterns:
            voter_match = re.search(pattern, text)
            if voter_match:
                data['Voter ID'] = voter_match.group(1)
                break

        return data

    def process_document(self, image_path, filename):
        """Main method to process a document"""
        # Extract text
        raw_text = self.extract_text_from_image(image_path)

        # Identify document type
        doc_type = self.identify_document_type(raw_text)

        # Create base result
        result = {
            'filename': filename,
            'Document Type': doc_type,
            'Raw Text': raw_text
        }

        # If document type is unknown, provide suggestions
        if doc_type == 'Unknown':
            suggestions = self.get_document_suggestions(raw_text)
            result['Suggestions'] = suggestions
            result['Manual_Classification_Needed'] = True
        else:
            # Extract structured data if document type is recognized
            structured_data = self.extract_structured_data(raw_text, doc_type)
            result.update(structured_data)

            # Learn from this document for future improvements
            self.doc_type_manager.learn_from_document(filename, doc_type, structured_data, raw_text)

        return result

    def get_document_suggestions(self, text):
        """Provide suggestions for unknown documents"""
        suggestions = {
            'possible_types': [],
            'detected_patterns': [],
            'keywords_found': [],
            'manual_hints': []
        }

        # Check for partial matches
        text_lower = text.lower()

        # Check each document type for partial matches
        for doc_type, config in self.doc_type_manager.document_types.items():
            keyword_matches = []
            pattern_matches = []

            # Check keywords
            for keyword in config['keywords']:
                if keyword in text_lower:
                    keyword_matches.append(keyword)

            # Check patterns
            for field, pattern in config['patterns'].items():
                if re.search(pattern, text, re.IGNORECASE):
                    pattern_matches.append(field)

            # If we have some matches, suggest this type
            if keyword_matches or pattern_matches:
                suggestions['possible_types'].append({
                    'type': doc_type,
                    'keywords_found': keyword_matches,
                    'patterns_found': pattern_matches,
                    'confidence': len(keyword_matches) + len(pattern_matches)
                })

        # Sort by confidence
        suggestions['possible_types'].sort(key=lambda x: x['confidence'], reverse=True)

        # Detect common patterns
        if re.search(r'[A-Z]{5}[0-9]{4}[A-Z]', text):
            suggestions['detected_patterns'].append('PAN Number format detected')

        if re.search(r'\d{4}\s*\d{4}\s*\d{4}', text):
            suggestions['detected_patterns'].append('12-digit number (possibly Aadhaar) detected')

        if re.search(r'[A-Z]{2}[0-9]{2}[A-Z0-9]+', text):
            suggestions['detected_patterns'].append('License number format detected')

        # Provide manual hints
        suggestions['manual_hints'] = [
            "Look for government logos or headers",
            "Check for specific number formats (PAN: **********, Aadhaar: 1234 5678 9012)",
            "Look for keywords like 'Income Tax', 'Aadhaar', 'Driving License'",
            "Check document layout and structure"
        ]

        return suggestions
